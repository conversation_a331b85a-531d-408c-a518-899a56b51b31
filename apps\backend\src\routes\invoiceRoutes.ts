import { Router } from 'express';
import { authenticateToken, requirePermission } from '@/middleware/auth';
import { createApiResponse } from '@ar-scia/shared-utils';
import { HILWorkflowService } from '../services/hilWorkflowService';

const router = Router();
const hilWorkflowService = new HILWorkflowService();
router.use(authenticateToken);

router.get('/', requirePermission('invoices:read'), (req, res) => {
  res.json(createApiResponse(true, [], undefined, 'Invoices retrieved successfully'));
});

export { router as invoiceRoutes };