import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Sheet, SheetContent, SheetDescription, SheetHeader, SheetTitle, SheetTrigger } from '@/components/ui/sheet';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import {
  Menu, Home, BarChart3, Package, Users, Settings,
  Bell, Search, User, LogOut, ChevronRight,
  DollarSign, Truck, AlertTriangle, TrendingUp, Brain
} from 'lucide-react';
import { useAuth } from '@/hooks/useAuth';

interface NavigationItem {
  id: string;
  label: string;
  icon: React.ReactNode;
  href: string;
  badge?: string;
  children?: NavigationItem[];
}

interface MobileNavigationProps {
  currentPath: string;
  onNavigate: (path: string) => void;
}

export const MobileNavigation: React.FC<MobileNavigationProps> = ({ currentPath, onNavigate }) => {
  const [isOpen, setIsOpen] = useState(false);
  const { user, logout } = useAuth();

  const navigationItems: NavigationItem[] = [
    {
      id: 'dashboard',
      label: 'Dashboard',
      icon: <Home className="w-5 h-5" />,
      href: '/dashboard'
    },
    {
      id: 'ar',
      label: 'Accounts Receivable',
      icon: <DollarSign className="w-5 h-5" />,
      href: '/ar',
      badge: '12',
      children: [
        {
          id: 'ar-dashboard',
          label: 'AR Dashboard',
          icon: <BarChart3 className="w-4 h-4" />,
          href: '/ar/dashboard'
        },
        {
          id: 'ar-invoices',
          label: 'Invoices',
          icon: <DollarSign className="w-4 h-4" />,
          href: '/ar/invoices'
        },
        {
          id: 'ar-collections',
          label: 'Collections',
          icon: <TrendingUp className="w-4 h-4" />,
          href: '/ar/collections'
        }
      ]
    },
    {
      id: 'supply-chain',
      label: 'Supply Chain',
      icon: <Truck className="w-5 h-5" />,
      href: '/supply-chain',
      badge: '3',
      children: [
        {
          id: 'sc-dashboard',
          label: 'SC Dashboard',
          icon: <BarChart3 className="w-4 h-4" />,
          href: '/supply-chain/dashboard'
        },
        {
          id: 'sc-shipments',
          label: 'Shipments',
          icon: <Package className="w-4 h-4" />,
          href: '/supply-chain/shipments'
        },
        {
          id: 'sc-tracking',
          label: 'Container Tracking',
          icon: <Truck className="w-4 h-4" />,
          href: '/supply-chain/tracking'
        }
      ]
    },
    {
      id: 'customers',
      label: 'Customers',
      icon: <Users className="w-5 h-5" />,
      href: '/customers',
      children: [
        {
          id: 'customer-list',
          label: 'Customer List',
          icon: <Users className="w-4 h-4" />,
          href: '/customers/list'
        },
        {
          id: 'customer-360',
          label: 'Customer 360',
          icon: <BarChart3 className="w-4 h-4" />,
          href: '/customers/360'
        }
      ]
    },
    {
      id: 'analytics',
      label: 'Advanced Analytics',
      icon: <BarChart3 className="w-5 h-5" />,
      href: '/analytics'
    },
    {
      id: 'alerts',
      label: 'Alerts & Exceptions',
      icon: <AlertTriangle className="w-5 h-5" />,
      href: '/alerts',
      badge: '7'
    },
    {
      id: 'hil-workflow',
      label: 'HIL Workflow',
      icon: <Brain className="w-5 h-5" />,
      href: '/hil-workflow',
      badge: '2'
    }
  ];

  const handleNavigation = (href: string) => {
    onNavigate(href);
    setIsOpen(false);
  };

  const renderNavigationItem = (item: NavigationItem, level: number = 0) => {
    const isActive = currentPath === item.href || currentPath.startsWith(item.href + '/');
    const hasChildren = item.children && item.children.length > 0;

    return (
      <div key={item.id} className={`${level > 0 ? 'ml-4' : ''}`}>
        <Button
          variant={isActive ? 'secondary' : 'ghost'}
          className={`w-full justify-start gap-3 h-12 ${level > 0 ? 'h-10 text-sm' : ''}`}
          onClick={() => handleNavigation(item.href)}
        >
          {item.icon}
          <span className="flex-1 text-left">{item.label}</span>
          {item.badge && (
            <Badge variant="destructive" className="ml-auto">
              {item.badge}
            </Badge>
          )}
          {hasChildren && <ChevronRight className="w-4 h-4 ml-auto" />}
        </Button>
        
        {hasChildren && (
          <div className="mt-1 space-y-1">
            {item.children!.map(child => renderNavigationItem(child, level + 1))}
          </div>
        )}
      </div>
    );
  };

  return (
    <Sheet open={isOpen} onOpenChange={setIsOpen}>
      <SheetTrigger asChild>
        <Button variant="ghost" size="icon" className="md:hidden">
          <Menu className="h-6 w-6" />
          <span className="sr-only">Toggle navigation menu</span>
        </Button>
      </SheetTrigger>
      <SheetContent side="left" className="w-80 p-0">
        <div className="flex flex-col h-full">
          {/* Header */}
          <SheetHeader className="p-6 pb-4">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-primary rounded-lg flex items-center justify-center">
                <BarChart3 className="w-6 h-6 text-primary-foreground" />
              </div>
              <div>
                <SheetTitle className="text-left">AR-SCIA</SheetTitle>
                <SheetDescription className="text-left">
                  Business Intelligence Platform
                </SheetDescription>
              </div>
            </div>
          </SheetHeader>

          {/* User Info */}
          <div className="px-6 pb-4">
            <div className="flex items-center gap-3 p-3 bg-muted rounded-lg">
              <div className="w-8 h-8 bg-primary rounded-full flex items-center justify-center">
                <User className="w-4 h-4 text-primary-foreground" />
              </div>
              <div className="flex-1 min-w-0">
                <div className="font-medium text-sm truncate">{user?.name || 'User'}</div>
                <div className="text-xs text-muted-foreground truncate">{user?.email || '<EMAIL>'}</div>
              </div>
            </div>
          </div>

          <Separator />

          {/* Navigation */}
          <div className="flex-1 overflow-y-auto p-6 space-y-2">
            {navigationItems.map(item => renderNavigationItem(item))}
          </div>

          <Separator />

          {/* Footer Actions */}
          <div className="p-6 space-y-2">
            <Button
              variant="ghost"
              className="w-full justify-start gap-3 h-12"
              onClick={() => handleNavigation('/notifications')}
            >
              <Bell className="w-5 h-5" />
              <span className="flex-1 text-left">Notifications</span>
              <Badge variant="destructive">3</Badge>
            </Button>
            
            <Button
              variant="ghost"
              className="w-full justify-start gap-3 h-12"
              onClick={() => handleNavigation('/search')}
            >
              <Search className="w-5 h-5" />
              <span className="flex-1 text-left">Search</span>
            </Button>
            
            <Button
              variant="ghost"
              className="w-full justify-start gap-3 h-12"
              onClick={() => handleNavigation('/settings')}
            >
              <Settings className="w-5 h-5" />
              <span className="flex-1 text-left">Settings</span>
            </Button>

            <Separator className="my-2" />
            
            <Button
              variant="ghost"
              className="w-full justify-start gap-3 h-12 text-red-600 hover:text-red-700 hover:bg-red-50"
              onClick={() => {
                logout();
                setIsOpen(false);
              }}
            >
              <LogOut className="w-5 h-5" />
              <span className="flex-1 text-left">Sign Out</span>
            </Button>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
};
