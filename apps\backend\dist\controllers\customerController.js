"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getCustomer360 = exports.deleteCustomer = exports.updateCustomer = exports.createCustomer = exports.getCustomerById = exports.getCustomers = void 0;
const customerService_1 = require("@/services/customerService");
const shared_utils_1 = require("@ar-scia/shared-utils");
const errorHandler_1 = require("@/middleware/errorHandler");
const customerService = new customerService_1.CustomerService();
exports.getCustomers = (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const result = await customerService.getCustomers(req.query);
    res.json((0, shared_utils_1.createApiResponse)(true, result, undefined, 'Customers retrieved successfully'));
});
exports.getCustomerById = (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { id } = req.params;
    const customer = await customerService.getCustomerById(id);
    res.json((0, shared_utils_1.createApiResponse)(true, customer, undefined, 'Customer retrieved successfully'));
});
exports.createCustomer = (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const customer = await customerService.createCustomer(req.body);
    res.status(201).json((0, shared_utils_1.createApiResponse)(true, customer, undefined, 'Customer created successfully'));
});
exports.updateCustomer = (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { id } = req.params;
    const customer = await customerService.updateCustomer(id, req.body);
    res.json((0, shared_utils_1.createApiResponse)(true, customer, undefined, 'Customer updated successfully'));
});
exports.deleteCustomer = (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { id } = req.params;
    const result = await customerService.deleteCustomer(id);
    res.json((0, shared_utils_1.createApiResponse)(true, result, undefined, 'Customer deleted successfully'));
});
exports.getCustomer360 = (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { id } = req.params;
    const result = await customerService.getCustomer360(id);
    res.json((0, shared_utils_1.createApiResponse)(true, result, undefined, 'Customer 360 view retrieved successfully'));
});
//# sourceMappingURL=customerController.js.map