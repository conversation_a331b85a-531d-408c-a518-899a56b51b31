import { Router, Request, Response } from 'express';
import { body, param, query, validationResult } from 'express-validator';
import { authenticateToken, requirePermissions } from '../middleware/auth';
import { responseService, WebhookPayload } from '../services/responseService';
import { AppError } from '@ar-scia/shared-utils';

const router = Router();

/**
 * POST /api/v1/responses/webhook
 * Webhook endpoint for capturing email responses
 * This endpoint should be publicly accessible for email providers
 */
router.post('/webhook',
  [
    body('emailId').notEmpty().withMessage('Email ID is required'),
    body('customerId').notEmpty().withMessage('Customer ID is required'),
    body('invoiceId').notEmpty().withMessage('Invoice ID is required'),
    body('responseContent').notEmpty().withMessage('Response content is required'),
    body('responseType').isIn(['email', 'phone', 'portal', 'webhook']).withMessage('Invalid response type'),
    body('timestamp').isISO8601().withMessage('Valid timestamp is required')
  ],
  async (req: Request, res: Response) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: 'Validation failed',
          details: errors.array()
        });
      }

      // Validate webhook payload
      const webhookPayload = responseService.validateWebhookPayload(req.body);
      
      // Process the response
      const processedResponse = await responseService.processEmailResponse(webhookPayload);

      res.status(201).json({
        success: true,
        data: {
          responseId: processedResponse.id,
          status: processedResponse.status,
          intention: processedResponse.customerIntention.type,
          confidence: processedResponse.customerIntention.confidence,
          followUpActions: processedResponse.followUpActions.length
        },
        message: 'Response processed successfully'
      });
    } catch (error) {
      console.error('Webhook processing error:', error);
      if (error instanceof AppError) {
        res.status(error.statusCode).json({
          success: false,
          error: error.message
        });
      } else {
        res.status(500).json({
          success: false,
          error: 'Failed to process response'
        });
      }
    }
  }
);

// Apply authentication to all routes below
router.use(authenticateToken);

/**
 * GET /api/v1/responses/:responseId
 * Get a specific response by ID
 */
router.get('/:responseId',
  requirePermissions(['responses:read']),
  [
    param('responseId').notEmpty().withMessage('Response ID is required')
  ],
  async (req: Request, res: Response) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: 'Validation failed',
          details: errors.array()
        });
      }

      const { responseId } = req.params;
      const response = await responseService.getResponseById(responseId);

      if (!response) {
        return res.status(404).json({
          success: false,
          error: 'Response not found'
        });
      }

      res.json({
        success: true,
        data: response,
        message: 'Response retrieved successfully'
      });
    } catch (error) {
      console.error('Get response error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve response'
      });
    }
  }
);

/**
 * GET /api/v1/responses/customer/:customerId
 * Get all responses for a specific customer
 */
router.get('/customer/:customerId',
  requirePermissions(['responses:read']),
  [
    param('customerId').notEmpty().withMessage('Customer ID is required')
  ],
  async (req: Request, res: Response) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: 'Validation failed',
          details: errors.array()
        });
      }

      const { customerId } = req.params;
      const responses = await responseService.getResponsesByCustomer(customerId);

      res.json({
        success: true,
        data: responses,
        message: 'Customer responses retrieved successfully'
      });
    } catch (error) {
      console.error('Get customer responses error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve customer responses'
      });
    }
  }
);

/**
 * GET /api/v1/responses/invoice/:invoiceId
 * Get all responses for a specific invoice
 */
router.get('/invoice/:invoiceId',
  requirePermissions(['responses:read']),
  [
    param('invoiceId').notEmpty().withMessage('Invoice ID is required')
  ],
  async (req: Request, res: Response) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: 'Validation failed',
          details: errors.array()
        });
      }

      const { invoiceId } = req.params;
      const responses = await responseService.getResponsesByInvoice(invoiceId);

      res.json({
        success: true,
        data: responses,
        message: 'Invoice responses retrieved successfully'
      });
    } catch (error) {
      console.error('Get invoice responses error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve invoice responses'
      });
    }
  }
);

/**
 * GET /api/v1/responses/actions
 * Get follow-up actions with optional filters
 */
router.get('/actions',
  requirePermissions(['responses:read']),
  [
    query('status').optional().isIn(['pending', 'in_progress', 'completed', 'cancelled']),
    query('priority').optional().isIn(['low', 'medium', 'high', 'urgent']),
    query('assignedTo').optional().isString(),
    query('dueDate').optional().isISO8601()
  ],
  async (req: Request, res: Response) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: 'Validation failed',
          details: errors.array()
        });
      }

      const filters = {
        status: req.query.status as string,
        priority: req.query.priority as string,
        assignedTo: req.query.assignedTo as string,
        dueDate: req.query.dueDate as string
      };

      // Remove undefined values
      Object.keys(filters).forEach(key => {
        if (filters[key as keyof typeof filters] === undefined) {
          delete filters[key as keyof typeof filters];
        }
      });

      const actions = await responseService.getFollowUpActions(filters);

      res.json({
        success: true,
        data: actions,
        message: 'Follow-up actions retrieved successfully'
      });
    } catch (error) {
      console.error('Get actions error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve follow-up actions'
      });
    }
  }
);

/**
 * PUT /api/v1/responses/actions/:actionId
 * Update a follow-up action
 */
router.put('/actions/:actionId',
  requirePermissions(['responses:write']),
  [
    param('actionId').notEmpty().withMessage('Action ID is required'),
    body('status').optional().isIn(['pending', 'in_progress', 'completed', 'cancelled']),
    body('priority').optional().isIn(['low', 'medium', 'high', 'urgent']),
    body('assignedTo').optional().isString(),
    body('dueDate').optional().isISO8601(),
    body('description').optional().isString()
  ],
  async (req: Request, res: Response) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: 'Validation failed',
          details: errors.array()
        });
      }

      const { actionId } = req.params;
      const updates = req.body;

      // Convert dueDate string to Date if provided
      if (updates.dueDate) {
        updates.dueDate = new Date(updates.dueDate);
      }

      const updatedAction = await responseService.updateFollowUpAction(actionId, updates);

      res.json({
        success: true,
        data: updatedAction,
        message: 'Follow-up action updated successfully'
      });
    } catch (error) {
      console.error('Update action error:', error);
      if (error instanceof AppError) {
        res.status(error.statusCode).json({
          success: false,
          error: error.message
        });
      } else {
        res.status(500).json({
          success: false,
          error: 'Failed to update follow-up action'
        });
      }
    }
  }
);

/**
 * POST /api/v1/responses/simulate
 * Simulate processing a response (for testing)
 */
router.post('/simulate',
  requirePermissions(['responses:write']),
  [
    body('responseContent').notEmpty().withMessage('Response content is required'),
    body('customerId').notEmpty().withMessage('Customer ID is required'),
    body('invoiceId').notEmpty().withMessage('Invoice ID is required')
  ],
  async (req: Request, res: Response) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: 'Validation failed',
          details: errors.array()
        });
      }

      const { responseContent, customerId, invoiceId } = req.body;

      const simulatedPayload: WebhookPayload = {
        emailId: `sim_${Date.now()}`,
        customerId,
        invoiceId,
        responseContent,
        responseType: 'email',
        timestamp: new Date().toISOString(),
        metadata: { simulated: true }
      };

      const processedResponse = await responseService.processEmailResponse(simulatedPayload);

      res.json({
        success: true,
        data: processedResponse,
        message: 'Response simulation completed successfully'
      });
    } catch (error) {
      console.error('Simulate response error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to simulate response processing'
      });
    }
  }
);

export default router;
