"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const helmet_1 = __importDefault(require("helmet"));
const express_rate_limit_1 = __importDefault(require("express-rate-limit"));
const config_1 = require("@/config/config");
const errorHandler_1 = require("@/middleware/errorHandler");
const requestLogger_1 = require("@/middleware/requestLogger");
const authRoutes_1 = require("@/routes/authRoutes");
const userRoutes_1 = require("@/routes/userRoutes");
const customerRoutes_1 = require("@/routes/customerRoutes");
const invoiceRoutes_1 = require("@/routes/invoiceRoutes");
const paymentRoutes_1 = require("@/routes/paymentRoutes");
const shipmentRoutes_1 = require("@/routes/shipmentRoutes");
const documentRoutes_1 = require("@/routes/documentRoutes");
const businessRuleRoutes_1 = require("@/routes/businessRuleRoutes");
const exceptionRoutes_1 = require("@/routes/exceptionRoutes");
const analyticsRoutes_1 = require("@/routes/analyticsRoutes");
const healthRoutes_1 = require("@/routes/healthRoutes");
const app = (0, express_1.default)();
app.use((0, helmet_1.default)({
    contentSecurityPolicy: {
        directives: {
            defaultSrc: ["'self'"],
            styleSrc: ["'self'", "'unsafe-inline'"],
            scriptSrc: ["'self'"],
            imgSrc: ["'self'", "data:", "https:"],
        },
    },
    crossOriginEmbedderPolicy: false,
}));
app.use((0, cors_1.default)({
    origin: config_1.config.corsOrigin,
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
}));
const limiter = (0, express_rate_limit_1.default)({
    windowMs: 15 * 60 * 1000,
    max: 100,
    message: {
        error: 'Too many requests from this IP, please try again later.',
    },
    standardHeaders: true,
    legacyHeaders: false,
});
app.use('/api/', limiter);
app.use(express_1.default.json({ limit: '10mb' }));
app.use(express_1.default.urlencoded({ extended: true, limit: '10mb' }));
app.use(requestLogger_1.requestLogger);
app.use('/health', healthRoutes_1.healthRoutes);
const apiRouter = express_1.default.Router();
apiRouter.use('/auth', authRoutes_1.authRoutes);
apiRouter.use('/users', userRoutes_1.userRoutes);
apiRouter.use('/customers', customerRoutes_1.customerRoutes);
apiRouter.use('/invoices', invoiceRoutes_1.invoiceRoutes);
apiRouter.use('/payments', paymentRoutes_1.paymentRoutes);
apiRouter.use('/shipments', shipmentRoutes_1.shipmentRoutes);
apiRouter.use('/documents', documentRoutes_1.documentRoutes);
apiRouter.use('/rules', businessRuleRoutes_1.businessRuleRoutes);
apiRouter.use('/exceptions', exceptionRoutes_1.exceptionRoutes);
apiRouter.use('/analytics', analyticsRoutes_1.analyticsRoutes);
app.use('/api/v1', apiRouter);
app.use('*', (req, res) => {
    res.status(404).json({
        success: false,
        error: 'Route not found',
        message: `Cannot ${req.method} ${req.originalUrl}`,
    });
});
app.use(errorHandler_1.errorHandler);
const PORT = config_1.config.port || 3001;
app.listen(PORT, () => {
    console.log(`🚀 Server running on port ${PORT}`);
    console.log(`📊 Environment: ${config_1.config.nodeEnv}`);
    console.log(`🔗 API Base URL: http://localhost:${PORT}/api/v1`);
    console.log(`🏥 Health Check: http://localhost:${PORT}/health`);
});
process.on('SIGTERM', () => {
    console.log('SIGTERM received, shutting down gracefully');
    process.exit(0);
});
process.on('SIGINT', () => {
    console.log('SIGINT received, shutting down gracefully');
    process.exit(0);
});
exports.default = app;
//# sourceMappingURL=index.js.map