"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.healthRoutes = void 0;
const express_1 = require("express");
const client_1 = require("@prisma/client");
const shared_utils_1 = require("@ar-scia/shared-utils");
const config_1 = require("@/config/config");
const router = (0, express_1.Router)();
exports.healthRoutes = router;
const prisma = new client_1.PrismaClient();
router.get('/', async (req, res) => {
    try {
        await prisma.$queryRaw `SELECT 1`;
        const healthData = {
            status: 'healthy',
            timestamp: new Date().toISOString(),
            environment: config_1.config.nodeEnv,
            version: '1.0.0',
            services: {
                database: 'connected',
                api: 'running'
            }
        };
        res.json((0, shared_utils_1.createApiResponse)(true, healthData, undefined, 'System is healthy'));
    }
    catch (error) {
        const healthData = {
            status: 'unhealthy',
            timestamp: new Date().toISOString(),
            environment: config_1.config.nodeEnv,
            version: '1.0.0',
            services: {
                database: 'disconnected',
                api: 'running'
            },
            error: error instanceof Error ? error.message : 'Unknown error'
        };
        res.status(503).json((0, shared_utils_1.createApiResponse)(false, healthData, 'System is unhealthy'));
    }
});
//# sourceMappingURL=healthRoutes.js.map