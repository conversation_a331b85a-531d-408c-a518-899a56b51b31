"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ROLE_PERMISSIONS = exports.UPLOAD_MAX_SIZE = exports.PASSWORD_MIN_LENGTH = exports.JWT_EXPIRES_IN = exports.MAX_PAGE_SIZE = exports.DEFAULT_PAGE_SIZE = exports.createApiResponse = exports.AppError = exports.createPaginationInfo = exports.transformPaginationParams = exports.extractPermissions = exports.hasPermission = exports.sanitizeInput = exports.validateEmail = exports.formatPhoneNumber = exports.capitalizeFirst = exports.truncateText = exports.slugify = exports.generateId = exports.getAgingBucket = exports.getDaysOverdue = exports.isOverdue = exports.calculateDaysBetween = exports.formatDateTime = exports.formatDate = exports.formatCurrency = exports.exceptionSchema = exports.businessRuleSchema = exports.shipmentSchema = exports.paymentSchema = exports.invoiceSchema = exports.customerSchema = exports.userSchema = void 0;
const zod_1 = require("zod");
const shared_types_1 = require("@ar-scia/shared-types");
// Validation Schemas
exports.userSchema = zod_1.z.object({
    email: zod_1.z.string().email(),
    firstName: zod_1.z.string().min(1),
    lastName: zod_1.z.string().min(1),
    password: zod_1.z.string().min(8).optional(),
    isActive: zod_1.z.boolean().default(true)
});
exports.customerSchema = zod_1.z.object({
    name: zod_1.z.string().min(1),
    email: zod_1.z.string().email().optional(),
    phone: zod_1.z.string().optional(),
    address: zod_1.z.string().optional(),
    priority: zod_1.z.nativeEnum(shared_types_1.Priority).default(shared_types_1.Priority.NORMAL),
    isActive: zod_1.z.boolean().default(true)
});
exports.invoiceSchema = zod_1.z.object({
    invoiceNumber: zod_1.z.string().min(1),
    customerId: zod_1.z.string().uuid(),
    amount: zod_1.z.number().positive(),
    dueDate: zod_1.z.date(),
    status: zod_1.z.nativeEnum(shared_types_1.InvoiceStatus).default(shared_types_1.InvoiceStatus.PENDING),
    xeroId: zod_1.z.string().optional()
});
exports.paymentSchema = zod_1.z.object({
    invoiceId: zod_1.z.string().uuid(),
    amount: zod_1.z.number().positive(),
    paymentDate: zod_1.z.date(),
    method: zod_1.z.nativeEnum(shared_types_1.PaymentMethod),
    reference: zod_1.z.string().optional()
});
exports.shipmentSchema = zod_1.z.object({
    shipmentNumber: zod_1.z.string().min(1),
    customerId: zod_1.z.string().uuid(),
    containerNumber: zod_1.z.string().optional(),
    status: zod_1.z.nativeEnum(shared_types_1.ShipmentStatus).default(shared_types_1.ShipmentStatus.PENDING),
    origin: zod_1.z.string().optional(),
    destination: zod_1.z.string().optional(),
    estimatedDate: zod_1.z.date().optional(),
    actualDate: zod_1.z.date().optional()
});
exports.businessRuleSchema = zod_1.z.object({
    name: zod_1.z.string().min(1),
    description: zod_1.z.string().optional(),
    domain: zod_1.z.nativeEnum(shared_types_1.RuleDomain),
    conditions: zod_1.z.any(),
    actions: zod_1.z.any(),
    isActive: zod_1.z.boolean().default(true),
    priority: zod_1.z.number().int().min(0).default(0)
});
exports.exceptionSchema = zod_1.z.object({
    type: zod_1.z.nativeEnum(shared_types_1.ExceptionType),
    severity: zod_1.z.nativeEnum(shared_types_1.Severity),
    title: zod_1.z.string().min(1),
    description: zod_1.z.string().min(1),
    status: zod_1.z.nativeEnum(shared_types_1.ExceptionStatus).default(shared_types_1.ExceptionStatus.OPEN),
    ruleId: zod_1.z.string().uuid().optional(),
    shipmentId: zod_1.z.string().uuid().optional(),
    customerId: zod_1.z.string().uuid().optional(),
    data: zod_1.z.any().optional()
});
// Utility Functions
const formatCurrency = (amount, currency = 'USD') => {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency
    }).format(amount);
};
exports.formatCurrency = formatCurrency;
const formatDate = (date, format = 'short') => {
    return new Intl.DateTimeFormat('en-US', {
        dateStyle: format
    }).format(date);
};
exports.formatDate = formatDate;
const formatDateTime = (date) => {
    return new Intl.DateTimeFormat('en-US', {
        dateStyle: 'short',
        timeStyle: 'short'
    }).format(date);
};
exports.formatDateTime = formatDateTime;
const calculateDaysBetween = (startDate, endDate) => {
    const diffTime = Math.abs(endDate.getTime() - startDate.getTime());
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
};
exports.calculateDaysBetween = calculateDaysBetween;
const isOverdue = (dueDate) => {
    return new Date() > dueDate;
};
exports.isOverdue = isOverdue;
const getDaysOverdue = (dueDate) => {
    if (!(0, exports.isOverdue)(dueDate))
        return 0;
    return (0, exports.calculateDaysBetween)(dueDate, new Date());
};
exports.getDaysOverdue = getDaysOverdue;
const getAgingBucket = (dueDate) => {
    const daysOverdue = (0, exports.getDaysOverdue)(dueDate);
    if (daysOverdue === 0)
        return 'Current';
    if (daysOverdue <= 30)
        return '1-30 days';
    if (daysOverdue <= 60)
        return '31-60 days';
    if (daysOverdue <= 90)
        return '61-90 days';
    return '90+ days';
};
exports.getAgingBucket = getAgingBucket;
const generateId = () => {
    return crypto.randomUUID();
};
exports.generateId = generateId;
const slugify = (text) => {
    return text
        .toLowerCase()
        .replace(/[^\w\s-]/g, '')
        .replace(/[\s_-]+/g, '-')
        .replace(/^-+|-+$/g, '');
};
exports.slugify = slugify;
const truncateText = (text, maxLength) => {
    if (text.length <= maxLength)
        return text;
    return text.substring(0, maxLength - 3) + '...';
};
exports.truncateText = truncateText;
const capitalizeFirst = (text) => {
    return text.charAt(0).toUpperCase() + text.slice(1).toLowerCase();
};
exports.capitalizeFirst = capitalizeFirst;
const formatPhoneNumber = (phone) => {
    const cleaned = phone.replace(/\D/g, '');
    const match = cleaned.match(/^(\d{3})(\d{3})(\d{4})$/);
    if (match) {
        return `(${match[1]}) ${match[2]}-${match[3]}`;
    }
    return phone;
};
exports.formatPhoneNumber = formatPhoneNumber;
const validateEmail = (email) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
};
exports.validateEmail = validateEmail;
const sanitizeInput = (input) => {
    return input.trim().replace(/[<>]/g, '');
};
exports.sanitizeInput = sanitizeInput;
// Permission Utilities
const hasPermission = (userPermissions, requiredPermission) => {
    if (userPermissions.includes('*'))
        return true;
    const [resource, action] = requiredPermission.split(':');
    return userPermissions.some(permission => {
        if (permission === requiredPermission)
            return true;
        const [permResource, permAction] = permission.split(':');
        if (permResource === resource && permAction === '*')
            return true;
        if (permResource === '*' && permAction === action)
            return true;
        return false;
    });
};
exports.hasPermission = hasPermission;
const extractPermissions = (roles) => {
    const permissions = new Set();
    roles.forEach(role => {
        role.permissions?.forEach((permission) => {
            permissions.add(`${permission.resource}:${permission.action}`);
        });
    });
    return Array.from(permissions);
};
exports.extractPermissions = extractPermissions;
// Data Transformation Utilities
const transformPaginationParams = (params) => {
    return {
        page: parseInt(params.page) || 1,
        limit: Math.min(parseInt(params.limit) || 10, 100),
        sortBy: params.sortBy || 'createdAt',
        sortOrder: params.sortOrder === 'asc' ? 'asc' : 'desc',
        search: params.search || ''
    };
};
exports.transformPaginationParams = transformPaginationParams;
const createPaginationInfo = (page, limit, total) => {
    const totalPages = Math.ceil(total / limit);
    return {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
    };
};
exports.createPaginationInfo = createPaginationInfo;
// Error Handling Utilities
class AppError extends Error {
    constructor(message, statusCode = 500, isOperational = true) {
        super(message);
        this.statusCode = statusCode;
        this.isOperational = isOperational;
        Error.captureStackTrace(this, this.constructor);
    }
}
exports.AppError = AppError;
const createApiResponse = (success, data, error, message, pagination) => {
    return {
        success,
        data,
        error,
        message,
        pagination
    };
};
exports.createApiResponse = createApiResponse;
// Constants
exports.DEFAULT_PAGE_SIZE = 10;
exports.MAX_PAGE_SIZE = 100;
exports.JWT_EXPIRES_IN = '24h';
exports.PASSWORD_MIN_LENGTH = 8;
exports.UPLOAD_MAX_SIZE = 10 * 1024 * 1024; // 10MB
exports.ROLE_PERMISSIONS = {
    SUPER_ADMIN: ['*'],
    FINANCE_MANAGER: [
        'customers:read', 'customers:write',
        'invoices:read', 'invoices:write', 'invoices:email',
        'payments:read', 'payments:write',
        'analytics:ar:read', 'reports:ar:export'
    ],
    SUPPLY_CHAIN_MANAGER: [
        'customers:read',
        'shipments:read', 'shipments:write',
        'documents:read', 'documents:write', 'documents:parse',
        'tracking:read', 'tracking:write',
        'exceptions:read', 'exceptions:write',
        'analytics:supply-chain:read'
    ],
    OPERATIONS_STAFF: [
        'customers:read',
        'invoices:read', 'shipments:read',
        'exceptions:read', 'exceptions:update',
        'documents:read'
    ],
    CUSTOMER_SERVICE: [
        'customers:read', 'customers:update',
        'invoices:read', 'shipments:read',
        'communications:read', 'communications:write',
        'customer-360:read'
    ]
};
