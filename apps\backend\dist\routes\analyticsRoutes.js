"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.analyticsRoutes = void 0;
const express_1 = require("express");
const auth_1 = require("@/middleware/auth");
const shared_utils_1 = require("@ar-scia/shared-utils");
const router = (0, express_1.Router)();
exports.analyticsRoutes = router;
router.use(auth_1.authenticateToken);
router.get('/dashboard', (0, auth_1.requirePermission)('analytics:ar:read'), (req, res) => {
    res.json((0, shared_utils_1.createApiResponse)(true, {}, undefined, 'Analytics retrieved successfully'));
});
//# sourceMappingURL=analyticsRoutes.js.map