import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { Loader2 } from 'lucide-react';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredPermissions?: string[];
  requiredRoles?: string[];
  requireAnyRole?: boolean; // If true, user needs ANY of the required roles, otherwise ALL
  fallbackPath?: string;
}

export const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requiredPermissions = [],
  requiredRoles = [],
  requireAnyRole = true,
  fallbackPath = '/login'
}) => {
  const { isAuthenticated, isLoading, user } = useAuth();
  const location = useLocation();

  // Show loading spinner while checking authentication
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p className="text-muted-foreground">Loading...</p>
        </div>
      </div>
    );
  }

  // Redirect to login if not authenticated
  if (!isAuthenticated) {
    return <Navigate to={fallbackPath} state={{ from: location }} replace />;
  }

  // Check role requirements
  if (requiredRoles.length > 0 && user) {
    const userRoles = user.roles.map(ur => ur.role.name);
    
    const hasRequiredRole = requireAnyRole
      ? requiredRoles.some(role => userRoles.includes(role))
      : requiredRoles.every(role => userRoles.includes(role));

    if (!hasRequiredRole) {
      return (
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">Access Denied</h2>
            <p className="text-gray-600 mb-4">
              You don't have the required permissions to access this page.
            </p>
            <p className="text-sm text-gray-500">
              Required roles: {requiredRoles.join(', ')}
            </p>
          </div>
        </div>
      );
    }
  }

  // Check permission requirements
  if (requiredPermissions.length > 0 && user) {
    const userPermissions: string[] = [];
    user.roles.forEach(userRole => {
      userRole.role.permissions.forEach(permission => {
        if (!userPermissions.includes(permission.name)) {
          userPermissions.push(permission.name);
        }
      });
    });

    const hasRequiredPermissions = requiredPermissions.every(permission =>
      userPermissions.includes(permission)
    );

    if (!hasRequiredPermissions) {
      return (
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">Access Denied</h2>
            <p className="text-gray-600 mb-4">
              You don't have the required permissions to access this page.
            </p>
            <p className="text-sm text-gray-500">
              Required permissions: {requiredPermissions.join(', ')}
            </p>
          </div>
        </div>
      );
    }
  }

  // User is authenticated and has required permissions/roles
  return <>{children}</>;
};

// Higher-order component for easier usage
export const withAuth = (
  Component: React.ComponentType<any>,
  options?: Omit<ProtectedRouteProps, 'children'>
) => {
  return (props: any) => (
    <ProtectedRoute {...options}>
      <Component {...props} />
    </ProtectedRoute>
  );
};

// Specific permission-based components
export const RequirePermission: React.FC<{
  permission: string;
  children: React.ReactNode;
  fallback?: React.ReactNode;
}> = ({ permission, children, fallback = null }) => {
  const { user } = useAuth();

  if (!user) return <>{fallback}</>;

  const userPermissions: string[] = [];
  user.roles.forEach(userRole => {
    userRole.role.permissions.forEach(perm => {
      if (!userPermissions.includes(perm.name)) {
        userPermissions.push(perm.name);
      }
    });
  });

  const hasPermission = userPermissions.includes(permission);

  return hasPermission ? <>{children}</> : <>{fallback}</>;
};

export const RequireRole: React.FC<{
  role: string;
  children: React.ReactNode;
  fallback?: React.ReactNode;
}> = ({ role, children, fallback = null }) => {
  const { user } = useAuth();

  if (!user) return <>{fallback}</>;

  const userRoles = user.roles.map(ur => ur.role.name);
  const hasRole = userRoles.includes(role);

  return hasRole ? <>{children}</> : <>{fallback}</>;
};

export const RequireAnyRole: React.FC<{
  roles: string[];
  children: React.ReactNode;
  fallback?: React.ReactNode;
}> = ({ roles, children, fallback = null }) => {
  const { user } = useAuth();

  if (!user) return <>{fallback}</>;

  const userRoles = user.roles.map(ur => ur.role.name);
  const hasAnyRole = roles.some(role => userRoles.includes(role));

  return hasAnyRole ? <>{children}</> : <>{fallback}</>;
};
