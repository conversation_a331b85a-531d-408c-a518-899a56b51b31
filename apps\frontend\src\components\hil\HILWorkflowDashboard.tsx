import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Progress } from '@/components/ui/progress';
import { 
  Clock, User, AlertTriangle, CheckCircle, Brain, 
  Target, TrendingUp, ArrowRight, RefreshCw, 
  MessageSquare, FileText, Zap, Shield, 
  Calendar, Users, DollarSign, Package
} from 'lucide-react';

interface HILTask {
  id: string;
  type: 'approval' | 'review' | 'decision' | 'exception_handling' | 'validation';
  title: string;
  description: string;
  priority: 'critical' | 'high' | 'medium' | 'low';
  urgency: 'immediate' | 'today' | 'this_week' | 'next_week';
  domain: 'ar' | 'supply_chain' | 'customer' | 'cross_domain';
  context: {
    customerId?: string;
    invoiceId?: string;
    shipmentId?: string;
    amount?: number;
    relatedData: Record<string, any>;
    businessRules: string[];
    riskFactors: string[];
  };
  aiRecommendation: {
    action: string;
    confidence: number;
    reasoning: string;
    alternatives: string[];
    riskAssessment: string;
  };
  assignedTo?: string;
  status: 'pending' | 'in_progress' | 'completed' | 'escalated' | 'cancelled';
  createdAt: Date;
  updatedAt: Date;
  dueDate: Date;
  escalationPath: string[];
  requiredApprovals: number;
  currentApprovals: number;
}

interface ContextualGuidance {
  summary: string;
  keyFactors: string[];
  recommendations: string[];
  risks: string[];
  similarCases: string[];
  nextSteps: string[];
}

export const HILWorkflowDashboard: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [tasks, setTasks] = useState<HILTask[]>([]);
  const [selectedTask, setSelectedTask] = useState<HILTask | null>(null);
  const [guidance, setGuidance] = useState<ContextualGuidance | null>(null);
  const [activeTab, setActiveTab] = useState('pending');
  const [filters, setFilters] = useState({
    domain: '',
    priority: '',
    status: 'pending'
  });

  useEffect(() => {
    loadTasks();
  }, [filters]);

  const loadTasks = async () => {
    setLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const mockTasks: HILTask[] = [
        {
          id: 'hil_001',
          type: 'approval',
          title: 'Credit Limit Increase Request',
          description: 'TechFlow Inc requesting credit limit increase from $100K to $150K',
          priority: 'high',
          urgency: 'today',
          domain: 'ar',
          context: {
            customerId: 'CUST001',
            amount: 50000,
            relatedData: {
              currentCreditLimit: 100000,
              requestedLimit: 150000,
              paymentHistory: 'excellent',
              currentBalance: 25000
            },
            businessRules: ['Credit increase requires manager approval', 'Payment history review required'],
            riskFactors: ['Large increase amount', 'Recent market volatility']
          },
          aiRecommendation: {
            action: 'Approve with conditions',
            confidence: 85,
            reasoning: 'Excellent payment history and strong financial position support approval',
            alternatives: ['Approve partial increase', 'Request additional documentation'],
            riskAssessment: 'Low risk based on payment history and financial stability'
          },
          assignedTo: 'current_user',
          status: 'pending',
          createdAt: new Date('2024-02-10T09:00:00'),
          updatedAt: new Date('2024-02-10T09:00:00'),
          dueDate: new Date('2024-02-10T17:00:00'),
          escalationPath: ['ar_manager', 'credit_director', 'cfo'],
          requiredApprovals: 1,
          currentApprovals: 0
        },
        {
          id: 'hil_002',
          type: 'exception_handling',
          title: 'Shipment Delay Exception',
          description: 'Container MSKU123456 delayed at Port of LA - customer impact assessment needed',
          priority: 'critical',
          urgency: 'immediate',
          domain: 'supply_chain',
          context: {
            customerId: 'CUST002',
            shipmentId: 'SHIP001',
            amount: 85000,
            relatedData: {
              delayReason: 'Port congestion',
              estimatedDelay: '3-5 days',
              customerPriority: 'high',
              alternativeOptions: ['Air freight', 'Partial shipment']
            },
            businessRules: ['High priority customers require immediate notification', 'Delays >3 days need approval'],
            riskFactors: ['Customer satisfaction impact', 'Potential penalty costs', 'Supply chain disruption']
          },
          aiRecommendation: {
            action: 'Expedite via air freight',
            confidence: 78,
            reasoning: 'High-value customer with time-sensitive delivery requirements',
            alternatives: ['Negotiate delay with customer', 'Partial air freight shipment'],
            riskAssessment: 'Medium risk - customer relationship and financial impact'
          },
          assignedTo: 'current_user',
          status: 'pending',
          createdAt: new Date('2024-02-10T08:30:00'),
          updatedAt: new Date('2024-02-10T08:30:00'),
          dueDate: new Date('2024-02-10T12:00:00'),
          escalationPath: ['logistics_manager', 'operations_director'],
          requiredApprovals: 1,
          currentApprovals: 0
        }
      ];

      setTasks(mockTasks.filter(task => {
        if (filters.domain && task.domain !== filters.domain) return false;
        if (filters.priority && task.priority !== filters.priority) return false;
        if (filters.status && task.status !== filters.status) return false;
        return true;
      }));
    } catch (error) {
      console.error('Failed to load tasks:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadGuidance = async (task: HILTask) => {
    setLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 800));
      
      const mockGuidance: ContextualGuidance = {
        summary: `${task.type} task requiring review of ${task.title.toLowerCase()} with ${task.priority} priority`,
        keyFactors: [
          'Customer payment history and financial stability',
          'Current market conditions and risk factors',
          'Business relationship value and strategic importance',
          'Regulatory compliance requirements'
        ],
        recommendations: [
          'Review customer payment history over last 24 months',
          'Assess current financial position and credit worthiness',
          'Consider market conditions and industry trends',
          'Evaluate relationship value and strategic importance'
        ],
        risks: [
          'Potential bad debt exposure if credit extended',
          'Customer relationship impact if request denied',
          'Competitive disadvantage if approval delayed',
          'Regulatory compliance implications'
        ],
        similarCases: [
          'Similar approval for GlobalTech Corp - approved with monitoring',
          'Manufacturing Plus request - approved with reduced amount',
          'TechSolutions Inc - denied due to payment issues'
        ],
        nextSteps: [
          'Review AI recommendation and confidence level',
          'Analyze customer data and payment patterns',
          'Consider business impact and relationship value',
          'Make decision and document reasoning',
          'Communicate decision to stakeholders'
        ]
      };

      setGuidance(mockGuidance);
    } catch (error) {
      console.error('Failed to load guidance:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleTaskSelect = (task: HILTask) => {
    setSelectedTask(task);
    loadGuidance(task);
  };

  const handleStatusUpdate = async (taskId: string, status: HILTask['status'], notes?: string) => {
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));
      
      setTasks(prev => prev.map(task => 
        task.id === taskId 
          ? { ...task, status, updatedAt: new Date() }
          : task
      ));

      if (selectedTask?.id === taskId) {
        setSelectedTask(prev => prev ? { ...prev, status, updatedAt: new Date() } : null);
      }
    } catch (error) {
      console.error('Failed to update task status:', error);
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'critical': return 'text-red-600 bg-red-50 border-red-200';
      case 'high': return 'text-orange-600 bg-orange-50 border-orange-200';
      case 'medium': return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'low': return 'text-green-600 bg-green-50 border-green-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getUrgencyIcon = (urgency: string) => {
    switch (urgency) {
      case 'immediate': return <Zap className="w-4 h-4 text-red-600" />;
      case 'today': return <Clock className="w-4 h-4 text-orange-600" />;
      case 'this_week': return <Calendar className="w-4 h-4 text-yellow-600" />;
      default: return <Calendar className="w-4 h-4 text-green-600" />;
    }
  };

  const getDomainIcon = (domain: string) => {
    switch (domain) {
      case 'ar': return <DollarSign className="w-4 h-4" />;
      case 'supply_chain': return <Package className="w-4 h-4" />;
      case 'customer': return <Users className="w-4 h-4" />;
      default: return <Target className="w-4 h-4" />;
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'approval': return <CheckCircle className="w-4 h-4" />;
      case 'review': return <FileText className="w-4 h-4" />;
      case 'decision': return <Brain className="w-4 h-4" />;
      case 'exception_handling': return <AlertTriangle className="w-4 h-4" />;
      default: return <Shield className="w-4 h-4" />;
    }
  };

  const formatTimeRemaining = (dueDate: Date) => {
    const now = new Date();
    const diffMs = dueDate.getTime() - now.getTime();
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffMins = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));

    if (diffMs < 0) {
      return 'Overdue';
    } else if (diffHours < 1) {
      return `${diffMins}m remaining`;
    } else if (diffHours < 24) {
      return `${diffHours}h ${diffMins}m remaining`;
    } else {
      const diffDays = Math.floor(diffHours / 24);
      return `${diffDays}d remaining`;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold">Human-in-the-Loop Workflow</h1>
          <p className="text-muted-foreground">AI-enhanced task prioritization and contextual guidance</p>
        </div>
        <div className="flex items-center gap-4">
          <Select value={filters.domain} onValueChange={(value) => setFilters(prev => ({ ...prev, domain: value }))}>
            <SelectTrigger className="w-40">
              <SelectValue placeholder="All Domains" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="">All Domains</SelectItem>
              <SelectItem value="ar">AR</SelectItem>
              <SelectItem value="supply_chain">Supply Chain</SelectItem>
              <SelectItem value="customer">Customer</SelectItem>
              <SelectItem value="cross_domain">Cross Domain</SelectItem>
            </SelectContent>
          </Select>
          <Select value={filters.priority} onValueChange={(value) => setFilters(prev => ({ ...prev, priority: value }))}>
            <SelectTrigger className="w-40">
              <SelectValue placeholder="All Priorities" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="">All Priorities</SelectItem>
              <SelectItem value="critical">Critical</SelectItem>
              <SelectItem value="high">High</SelectItem>
              <SelectItem value="medium">Medium</SelectItem>
              <SelectItem value="low">Low</SelectItem>
            </SelectContent>
          </Select>
          <Button onClick={loadTasks} variant="outline" disabled={loading}>
            <RefreshCw className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Task List */}
        <div className="lg:col-span-1 space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Clock className="w-5 h-5" />
                Pending Tasks ({tasks.length})
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {loading && tasks.length === 0 ? (
                <div className="text-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
                  <p className="mt-2 text-muted-foreground">Loading tasks...</p>
                </div>
              ) : tasks.length === 0 ? (
                <div className="text-center py-8">
                  <CheckCircle className="w-12 h-12 mx-auto text-green-600 mb-2" />
                  <p className="text-muted-foreground">No pending tasks</p>
                </div>
              ) : (
                tasks.map((task) => (
                  <Card 
                    key={task.id} 
                    className={`cursor-pointer transition-all hover:shadow-md ${
                      selectedTask?.id === task.id ? 'ring-2 ring-primary' : ''
                    }`}
                    onClick={() => handleTaskSelect(task)}
                  >
                    <CardContent className="p-4">
                      <div className="flex items-start justify-between mb-2">
                        <div className="flex items-center gap-2">
                          {getTypeIcon(task.type)}
                          <span className="font-medium text-sm">{task.title}</span>
                        </div>
                        {getUrgencyIcon(task.urgency)}
                      </div>
                      
                      <p className="text-xs text-muted-foreground mb-3 line-clamp-2">
                        {task.description}
                      </p>
                      
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <Badge className={getPriorityColor(task.priority)}>
                            {task.priority}
                          </Badge>
                          <Badge variant="outline" className="text-xs">
                            {getDomainIcon(task.domain)}
                            <span className="ml-1">{task.domain.replace('_', ' ')}</span>
                          </Badge>
                        </div>
                        <div className="text-xs text-muted-foreground">
                          {formatTimeRemaining(task.dueDate)}
                        </div>
                      </div>
                      
                      {task.context.amount && (
                        <div className="mt-2 text-xs">
                          <span className="text-muted-foreground">Amount: </span>
                          <span className="font-medium">
                            ${task.context.amount.toLocaleString()}
                          </span>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                ))
              )}
            </CardContent>
          </Card>
        </div>

        {/* Task Details and Guidance */}
        <div className="lg:col-span-2">
          {selectedTask ? (
            <div className="space-y-6">
              {/* Task Details */}
              <Card>
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <div>
                      <CardTitle className="flex items-center gap-2">
                        {getTypeIcon(selectedTask.type)}
                        {selectedTask.title}
                      </CardTitle>
                      <CardDescription>{selectedTask.description}</CardDescription>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge className={getPriorityColor(selectedTask.priority)}>
                        {selectedTask.priority}
                      </Badge>
                      <Badge variant="outline">
                        {getDomainIcon(selectedTask.domain)}
                        <span className="ml-1">{selectedTask.domain.replace('_', ' ')}</span>
                      </Badge>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* AI Recommendation */}
                  <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                    <div className="flex items-center gap-2 mb-2">
                      <Brain className="w-5 h-5 text-blue-600" />
                      <span className="font-medium text-blue-900">AI Recommendation</span>
                      <Badge variant="outline" className="text-blue-700">
                        {selectedTask.aiRecommendation.confidence}% confidence
                      </Badge>
                    </div>
                    <p className="text-blue-800 mb-2">{selectedTask.aiRecommendation.action}</p>
                    <p className="text-sm text-blue-700">{selectedTask.aiRecommendation.reasoning}</p>

                    {selectedTask.aiRecommendation.alternatives.length > 0 && (
                      <div className="mt-3">
                        <span className="text-sm font-medium text-blue-900">Alternatives:</span>
                        <ul className="text-sm text-blue-700 mt-1 space-y-1">
                          {selectedTask.aiRecommendation.alternatives.map((alt, index) => (
                            <li key={index} className="flex items-start gap-2">
                              <ArrowRight className="w-3 h-3 mt-0.5 flex-shrink-0" />
                              {alt}
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </div>

                  {/* Context Information */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <h4 className="font-medium mb-2">Business Rules</h4>
                      <ul className="space-y-1">
                        {selectedTask.context.businessRules.map((rule, index) => (
                          <li key={index} className="text-sm flex items-start gap-2">
                            <CheckCircle className="w-3 h-3 text-green-600 mt-0.5 flex-shrink-0" />
                            {rule}
                          </li>
                        ))}
                      </ul>
                    </div>

                    <div>
                      <h4 className="font-medium mb-2">Risk Factors</h4>
                      <ul className="space-y-1">
                        {selectedTask.context.riskFactors.map((risk, index) => (
                          <li key={index} className="text-sm flex items-start gap-2">
                            <AlertTriangle className="w-3 h-3 text-red-600 mt-0.5 flex-shrink-0" />
                            {risk}
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>

                  {/* Task Metadata */}
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 pt-4 border-t">
                    <div>
                      <span className="text-sm text-muted-foreground">Created</span>
                      <div className="font-medium">{selectedTask.createdAt.toLocaleDateString()}</div>
                    </div>
                    <div>
                      <span className="text-sm text-muted-foreground">Due Date</span>
                      <div className="font-medium">{selectedTask.dueDate.toLocaleDateString()}</div>
                    </div>
                    <div>
                      <span className="text-sm text-muted-foreground">Approvals</span>
                      <div className="font-medium">
                        {selectedTask.currentApprovals}/{selectedTask.requiredApprovals}
                      </div>
                    </div>
                    <div>
                      <span className="text-sm text-muted-foreground">Time Remaining</span>
                      <div className="font-medium">{formatTimeRemaining(selectedTask.dueDate)}</div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Contextual Guidance */}
              {guidance && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Target className="w-5 h-5 text-purple-600" />
                      Contextual Guidance
                    </CardTitle>
                    <CardDescription>AI-powered insights and recommendations</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <Tabs defaultValue="summary" className="w-full">
                      <TabsList className="grid w-full grid-cols-4">
                        <TabsTrigger value="summary">Summary</TabsTrigger>
                        <TabsTrigger value="factors">Key Factors</TabsTrigger>
                        <TabsTrigger value="recommendations">Recommendations</TabsTrigger>
                        <TabsTrigger value="next-steps">Next Steps</TabsTrigger>
                      </TabsList>

                      <TabsContent value="summary" className="space-y-4">
                        <p className="text-muted-foreground">{guidance.summary}</p>

                        <div>
                          <h4 className="font-medium mb-2">Risk Assessment</h4>
                          <Alert>
                            <Shield className="h-4 w-4" />
                            <AlertDescription>
                              {selectedTask.aiRecommendation.riskAssessment}
                            </AlertDescription>
                          </Alert>
                        </div>
                      </TabsContent>

                      <TabsContent value="factors" className="space-y-3">
                        {guidance.keyFactors.map((factor, index) => (
                          <div key={index} className="flex items-start gap-3 p-3 bg-muted rounded-lg">
                            <TrendingUp className="w-4 h-4 text-blue-600 mt-0.5 flex-shrink-0" />
                            <span className="text-sm">{factor}</span>
                          </div>
                        ))}
                      </TabsContent>

                      <TabsContent value="recommendations" className="space-y-3">
                        {guidance.recommendations.map((rec, index) => (
                          <div key={index} className="flex items-start gap-3 p-3 bg-green-50 border border-green-200 rounded-lg">
                            <CheckCircle className="w-4 h-4 text-green-600 mt-0.5 flex-shrink-0" />
                            <span className="text-sm text-green-800">{rec}</span>
                          </div>
                        ))}
                      </TabsContent>

                      <TabsContent value="next-steps" className="space-y-3">
                        {guidance.nextSteps.map((step, index) => (
                          <div key={index} className="flex items-start gap-3 p-3 border rounded-lg">
                            <div className="w-6 h-6 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-xs font-medium flex-shrink-0">
                              {index + 1}
                            </div>
                            <span className="text-sm">{step}</span>
                          </div>
                        ))}
                      </TabsContent>
                    </Tabs>
                  </CardContent>
                </Card>
              )}

              {/* Action Buttons */}
              <Card>
                <CardHeader>
                  <CardTitle>Take Action</CardTitle>
                  <CardDescription>Make a decision on this task</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex flex-wrap gap-3">
                    <Button
                      onClick={() => handleStatusUpdate(selectedTask.id, 'completed')}
                      className="bg-green-600 hover:bg-green-700"
                    >
                      <CheckCircle className="w-4 h-4 mr-2" />
                      Approve
                    </Button>

                    <Button
                      variant="destructive"
                      onClick={() => handleStatusUpdate(selectedTask.id, 'cancelled')}
                    >
                      <AlertTriangle className="w-4 h-4 mr-2" />
                      Reject
                    </Button>

                    <Button
                      variant="outline"
                      onClick={() => handleStatusUpdate(selectedTask.id, 'escalated')}
                    >
                      <ArrowRight className="w-4 h-4 mr-2" />
                      Escalate
                    </Button>

                    <Button
                      variant="outline"
                      onClick={() => handleStatusUpdate(selectedTask.id, 'in_progress')}
                    >
                      <Clock className="w-4 h-4 mr-2" />
                      Need More Info
                    </Button>
                  </div>

                  <div>
                    <label className="text-sm font-medium mb-2 block">Decision Notes</label>
                    <Textarea
                      placeholder="Add notes about your decision..."
                      className="min-h-[80px]"
                    />
                  </div>

                  <Button className="w-full">
                    <MessageSquare className="w-4 h-4 mr-2" />
                    Submit Decision
                  </Button>
                </CardContent>
              </Card>
            </div>
          ) : (
            <Card className="h-96 flex items-center justify-center">
              <div className="text-center">
                <FileText className="w-12 h-12 mx-auto text-muted-foreground mb-4" />
                <h3 className="text-lg font-medium mb-2">Select a Task</h3>
                <p className="text-muted-foreground">
                  Choose a task from the list to view details and AI-powered guidance
                </p>
              </div>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
};
