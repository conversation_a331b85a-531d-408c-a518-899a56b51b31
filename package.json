{"name": "ar-scia-unified-platform", "version": "1.0.0", "description": "Unified Business Intelligence Agent for AR Automation and Supply Chain Intelligence", "private": true, "workspaces": ["apps/*", "packages/*"], "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:backend": "npm run dev --workspace=apps/backend", "dev:frontend": "npm run dev --workspace=apps/frontend", "build": "npm run build --workspaces", "build:backend": "npm run build --workspace=apps/backend", "build:frontend": "npm run build --workspace=apps/frontend", "test": "npm run test --workspaces", "lint": "npm run lint --workspaces", "type-check": "npm run type-check --workspaces", "db:generate": "npm run db:generate --workspace=apps/backend", "db:migrate": "npm run db:migrate --workspace=apps/backend", "db:seed": "npm run db:seed --workspace=apps/backend", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "docker:logs": "docker-compose logs -f"}, "devDependencies": {"@types/node": "^20.10.0", "concurrently": "^8.2.2", "typescript": "^5.3.0"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "author": "ISG Development Team", "license": "MIT", "dependencies": {"dotenv": "^17.0.0"}}