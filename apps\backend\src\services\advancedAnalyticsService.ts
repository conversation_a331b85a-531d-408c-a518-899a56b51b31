import { GoogleGenerativeAI } from '@google/generative-ai';
import { AppError } from '../utils/errors';

interface AnalyticsQuery {
  domains: ('ar' | 'supply_chain' | 'customer' | 'financial')[];
  timeframe: {
    start: Date;
    end: Date;
    granularity: 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'yearly';
  };
  metrics: string[];
  filters: Record<string, any>;
  groupBy?: string[];
  includeAIInsights: boolean;
}

interface KPIMetric {
  id: string;
  name: string;
  value: number;
  unit: string;
  trend: 'up' | 'down' | 'stable';
  trendPercentage: number;
  target?: number;
  status: 'good' | 'warning' | 'critical';
  description: string;
  domain: string;
}

interface TrendAnalysis {
  metric: string;
  timeframe: string;
  data: { period: string; value: number; prediction?: number }[];
  trend: 'increasing' | 'decreasing' | 'stable' | 'volatile';
  seasonality: boolean;
  forecast: { period: string; value: number; confidence: number }[];
  insights: string[];
}

interface CrossDomainCorrelation {
  metric1: string;
  metric2: string;
  correlation: number;
  strength: 'strong' | 'moderate' | 'weak';
  direction: 'positive' | 'negative';
  significance: number;
  insights: string;
}

interface AIInsight {
  id: string;
  type: 'opportunity' | 'risk' | 'trend' | 'anomaly' | 'recommendation';
  title: string;
  description: string;
  confidence: number;
  impact: 'high' | 'medium' | 'low';
  urgency: 'immediate' | 'short_term' | 'long_term';
  affectedDomains: string[];
  recommendedActions: string[];
  supportingData: Record<string, any>;
}

interface ExecutiveReport {
  id: string;
  title: string;
  generatedDate: Date;
  timeframe: { start: Date; end: Date };
  executiveSummary: string;
  keyMetrics: KPIMetric[];
  criticalInsights: AIInsight[];
  performanceHighlights: string[];
  areasOfConcern: string[];
  strategicRecommendations: string[];
  nextSteps: string[];
  appendices: {
    detailedMetrics: KPIMetric[];
    trendAnalysis: TrendAnalysis[];
    correlationAnalysis: CrossDomainCorrelation[];
  };
}

interface AnalyticsResult {
  query: AnalyticsQuery;
  executedAt: Date;
  kpis: KPIMetric[];
  trends: TrendAnalysis[];
  correlations: CrossDomainCorrelation[];
  aiInsights: AIInsight[];
  executiveReport?: ExecutiveReport;
}

class AdvancedAnalyticsService {
  private genAI: GoogleGenerativeAI;
  private model: any;

  constructor() {
    if (!process.env.GOOGLE_AI_API_KEY) {
      throw new AppError('Google AI API key not configured', 500);
    }
    this.genAI = new GoogleGenerativeAI(process.env.GOOGLE_AI_API_KEY);
    this.model = this.genAI.getGenerativeModel({ model: 'gemini-pro' });
  }

  /**
   * Execute advanced analytics query with AI insights
   */
  async executeAnalyticsQuery(query: AnalyticsQuery): Promise<AnalyticsResult> {
    try {
      // Get base metrics
      const kpis = await this.calculateKPIs(query);
      
      // Perform trend analysis
      const trends = await this.analyzeTrends(query);
      
      // Calculate cross-domain correlations
      const correlations = await this.calculateCorrelations(query);
      
      // Generate AI insights if requested
      let aiInsights: AIInsight[] = [];
      if (query.includeAIInsights) {
        aiInsights = await this.generateAIInsights(kpis, trends, correlations, query);
      }

      return {
        query,
        executedAt: new Date(),
        kpis,
        trends,
        correlations,
        aiInsights
      };
    } catch (error) {
      console.error('Analytics query execution error:', error);
      throw new AppError('Failed to execute analytics query', 500);
    }
  }

  /**
   * Generate executive report with AI-powered insights
   */
  async generateExecutiveReport(
    timeframe: { start: Date; end: Date },
    includeForecasting: boolean = true
  ): Promise<ExecutiveReport> {
    try {
      const query: AnalyticsQuery = {
        domains: ['ar', 'supply_chain', 'customer', 'financial'],
        timeframe: {
          start: timeframe.start,
          end: timeframe.end,
          granularity: 'monthly'
        },
        metrics: ['revenue', 'collections', 'shipments', 'customer_satisfaction', 'costs'],
        filters: {},
        includeAIInsights: true
      };

      const analyticsResult = await this.executeAnalyticsQuery(query);
      
      // Generate executive summary with AI
      const executiveSummary = await this.generateExecutiveSummary(analyticsResult);
      
      // Identify critical insights
      const criticalInsights = analyticsResult.aiInsights
        .filter(insight => insight.impact === 'high' || insight.urgency === 'immediate')
        .slice(0, 5);

      // Generate strategic recommendations
      const strategicRecommendations = await this.generateStrategicRecommendations(analyticsResult);

      const report: ExecutiveReport = {
        id: `exec-report-${Date.now()}`,
        title: `Executive Business Intelligence Report - ${this.formatDateRange(timeframe)}`,
        generatedDate: new Date(),
        timeframe,
        executiveSummary,
        keyMetrics: analyticsResult.kpis.filter(kpi => kpi.status !== 'good').slice(0, 8),
        criticalInsights,
        performanceHighlights: this.extractPerformanceHighlights(analyticsResult),
        areasOfConcern: this.extractAreasOfConcern(analyticsResult),
        strategicRecommendations,
        nextSteps: await this.generateNextSteps(analyticsResult),
        appendices: {
          detailedMetrics: analyticsResult.kpis,
          trendAnalysis: analyticsResult.trends,
          correlationAnalysis: analyticsResult.correlations
        }
      };

      return report;
    } catch (error) {
      console.error('Executive report generation error:', error);
      throw new AppError('Failed to generate executive report', 500);
    }
  }

  /**
   * Calculate KPIs across domains
   */
  private async calculateKPIs(query: AnalyticsQuery): Promise<KPIMetric[]> {
    // In a real implementation, this would query actual data
    const mockKPIs: KPIMetric[] = [
      {
        id: 'ar-collection-rate',
        name: 'Collection Rate',
        value: 87.3,
        unit: '%',
        trend: 'up',
        trendPercentage: 2.1,
        target: 90,
        status: 'warning',
        description: 'Percentage of receivables collected within terms',
        domain: 'ar'
      },
      {
        id: 'ar-dso',
        name: 'Days Sales Outstanding',
        value: 35.2,
        unit: 'days',
        trend: 'down',
        trendPercentage: -1.8,
        target: 30,
        status: 'warning',
        description: 'Average days to collect receivables',
        domain: 'ar'
      },
      {
        id: 'sc-otd',
        name: 'On-Time Delivery',
        value: 92.1,
        unit: '%',
        trend: 'up',
        trendPercentage: 1.5,
        target: 95,
        status: 'good',
        description: 'Percentage of shipments delivered on time',
        domain: 'supply_chain'
      },
      {
        id: 'sc-cost-per-shipment',
        name: 'Cost per Shipment',
        value: 1250,
        unit: '$',
        trend: 'up',
        trendPercentage: 3.2,
        target: 1200,
        status: 'warning',
        description: 'Average cost per shipment including all fees',
        domain: 'supply_chain'
      },
      {
        id: 'customer-satisfaction',
        name: 'Customer Satisfaction',
        value: 4.2,
        unit: '/5',
        trend: 'stable',
        trendPercentage: 0.1,
        target: 4.5,
        status: 'good',
        description: 'Average customer satisfaction score',
        domain: 'customer'
      },
      {
        id: 'customer-retention',
        name: 'Customer Retention',
        value: 94.5,
        unit: '%',
        trend: 'up',
        trendPercentage: 0.8,
        target: 95,
        status: 'good',
        description: 'Percentage of customers retained year-over-year',
        domain: 'customer'
      }
    ];

    return mockKPIs;
  }

  /**
   * Analyze trends with forecasting
   */
  private async analyzeTrends(query: AnalyticsQuery): Promise<TrendAnalysis[]> {
    // Mock trend analysis data
    const mockTrends: TrendAnalysis[] = [
      {
        metric: 'Collection Rate',
        timeframe: 'Last 12 months',
        data: [
          { period: '2023-03', value: 85.2 },
          { period: '2023-04', value: 86.1 },
          { period: '2023-05', value: 84.8 },
          { period: '2023-06', value: 87.3 },
          { period: '2023-07', value: 88.1 },
          { period: '2023-08', value: 86.9 },
          { period: '2023-09', value: 87.8 },
          { period: '2023-10', value: 88.5 },
          { period: '2023-11', value: 87.2 },
          { period: '2023-12', value: 89.1 },
          { period: '2024-01', value: 87.8 },
          { period: '2024-02', value: 87.3 }
        ],
        trend: 'stable',
        seasonality: true,
        forecast: [
          { period: '2024-03', value: 88.2, confidence: 85 },
          { period: '2024-04', value: 88.8, confidence: 82 },
          { period: '2024-05', value: 87.9, confidence: 78 }
        ],
        insights: [
          'Collection rates show seasonal variation with Q4 typically performing better',
          'Recent trend suggests stabilization around 87-89% range',
          'Opportunity to improve through enhanced follow-up processes'
        ]
      },
      {
        metric: 'On-Time Delivery',
        timeframe: 'Last 12 months',
        data: [
          { period: '2023-03', value: 89.5 },
          { period: '2023-04', value: 91.2 },
          { period: '2023-05', value: 90.8 },
          { period: '2023-06', value: 92.1 },
          { period: '2023-07', value: 91.8 },
          { period: '2023-08', value: 90.3 },
          { period: '2023-09', value: 92.5 },
          { period: '2023-10', value: 91.9 },
          { period: '2023-11', value: 90.7 },
          { period: '2023-12', value: 88.9 },
          { period: '2024-01', value: 91.8 },
          { period: '2024-02', value: 92.1 }
        ],
        trend: 'increasing',
        seasonality: true,
        forecast: [
          { period: '2024-03', value: 92.8, confidence: 88 },
          { period: '2024-04', value: 93.2, confidence: 85 },
          { period: '2024-05', value: 92.9, confidence: 82 }
        ],
        insights: [
          'Steady improvement in on-time delivery performance',
          'December typically shows seasonal dip due to holiday volume',
          'Current trajectory suggests reaching 95% target by Q3'
        ]
      }
    ];

    return mockTrends;
  }

  /**
   * Calculate cross-domain correlations
   */
  private async calculateCorrelations(query: AnalyticsQuery): Promise<CrossDomainCorrelation[]> {
    const mockCorrelations: CrossDomainCorrelation[] = [
      {
        metric1: 'On-Time Delivery',
        metric2: 'Customer Satisfaction',
        correlation: 0.78,
        strength: 'strong',
        direction: 'positive',
        significance: 0.95,
        insights: 'Strong positive correlation between delivery performance and customer satisfaction. Improving on-time delivery by 1% typically increases satisfaction by 0.78 points.'
      },
      {
        metric1: 'Collection Rate',
        metric2: 'Customer Retention',
        correlation: -0.42,
        strength: 'moderate',
        direction: 'negative',
        significance: 0.87,
        insights: 'Moderate negative correlation suggests aggressive collection practices may impact customer retention. Balance needed between cash flow and relationship management.'
      },
      {
        metric1: 'Days Sales Outstanding',
        metric2: 'Shipment Delays',
        correlation: 0.65,
        strength: 'strong',
        direction: 'positive',
        significance: 0.92,
        insights: 'Customers with payment delays often experience shipment delays, suggesting potential credit hold policies affecting service levels.'
      }
    ];

    return mockCorrelations;
  }

  /**
   * Generate AI-powered insights
   */
  private async generateAIInsights(
    kpis: KPIMetric[],
    trends: TrendAnalysis[],
    correlations: CrossDomainCorrelation[],
    query: AnalyticsQuery
  ): Promise<AIInsight[]> {
    const prompt = `
    Analyze the following business intelligence data and provide strategic insights:

    KEY PERFORMANCE INDICATORS:
    ${kpis.map(kpi => `- ${kpi.name}: ${kpi.value}${kpi.unit} (${kpi.trend} ${kpi.trendPercentage}%, Target: ${kpi.target || 'N/A'})`).join('\n')}

    TREND ANALYSIS:
    ${trends.map(trend => `- ${trend.metric}: ${trend.trend} trend over ${trend.timeframe}`).join('\n')}

    CORRELATIONS:
    ${correlations.map(corr => `- ${corr.metric1} vs ${corr.metric2}: ${corr.correlation} (${corr.strength} ${corr.direction})`).join('\n')}

    Provide insights in the following categories:
    1. Opportunities for improvement
    2. Risk factors requiring attention
    3. Emerging trends to monitor
    4. Anomalies or unusual patterns
    5. Strategic recommendations

    Format as JSON array with objects containing: type, title, description, confidence, impact, urgency, affectedDomains, recommendedActions
    `;

    try {
      const result = await this.model.generateContent(prompt);
      const response = await result.response;
      const text = response.text();

      // Parse AI response
      const jsonMatch = text.match(/\[[\s\S]*\]/);
      if (jsonMatch) {
        const insights = JSON.parse(jsonMatch[0]);
        return insights.map((insight: any, index: number) => ({
          id: `ai-insight-${index}`,
          type: insight.type || 'recommendation',
          title: insight.title || 'AI Insight',
          description: insight.description || '',
          confidence: insight.confidence || 75,
          impact: insight.impact || 'medium',
          urgency: insight.urgency || 'short_term',
          affectedDomains: insight.affectedDomains || ['general'],
          recommendedActions: insight.recommendedActions || [],
          supportingData: { kpis: kpis.length, trends: trends.length, correlations: correlations.length }
        }));
      }

      // Fallback insights if AI parsing fails
      return this.generateFallbackInsights(kpis, trends, correlations);
    } catch (error) {
      console.error('AI insights generation error:', error);
      return this.generateFallbackInsights(kpis, trends, correlations);
    }
  }

  /**
   * Generate fallback insights when AI is unavailable
   */
  private generateFallbackInsights(
    kpis: KPIMetric[],
    trends: TrendAnalysis[],
    correlations: CrossDomainCorrelation[]
  ): AIInsight[] {
    const insights: AIInsight[] = [];

    // Identify KPIs missing targets
    const underperformingKPIs = kpis.filter(kpi => kpi.status === 'warning' || kpi.status === 'critical');
    if (underperformingKPIs.length > 0) {
      insights.push({
        id: 'underperforming-kpis',
        type: 'risk',
        title: 'Underperforming KPIs Require Attention',
        description: `${underperformingKPIs.length} KPIs are not meeting targets: ${underperformingKPIs.map(k => k.name).join(', ')}`,
        confidence: 90,
        impact: 'high',
        urgency: 'short_term',
        affectedDomains: [...new Set(underperformingKPIs.map(k => k.domain))],
        recommendedActions: ['Review target feasibility', 'Implement improvement initiatives', 'Increase monitoring frequency'],
        supportingData: { underperformingCount: underperformingKPIs.length }
      });
    }

    // Identify positive trends
    const improvingTrends = trends.filter(trend => trend.trend === 'increasing');
    if (improvingTrends.length > 0) {
      insights.push({
        id: 'positive-trends',
        type: 'opportunity',
        title: 'Positive Performance Trends Identified',
        description: `${improvingTrends.length} metrics showing improvement: ${improvingTrends.map(t => t.metric).join(', ')}`,
        confidence: 85,
        impact: 'medium',
        urgency: 'long_term',
        affectedDomains: ['general'],
        recommendedActions: ['Sustain current initiatives', 'Scale successful practices', 'Document best practices'],
        supportingData: { improvingCount: improvingTrends.length }
      });
    }

    return insights;
  }

  /**
   * Generate executive summary using AI
   */
  private async generateExecutiveSummary(analyticsResult: AnalyticsResult): Promise<string> {
    const prompt = `
    Create an executive summary for a business intelligence report based on the following data:

    KEY METRICS:
    ${analyticsResult.kpis.map(kpi => `- ${kpi.name}: ${kpi.value}${kpi.unit} (${kpi.status})`).join('\n')}

    AI INSIGHTS:
    ${analyticsResult.aiInsights.map(insight => `- ${insight.title}: ${insight.description}`).join('\n')}

    Write a concise 2-3 paragraph executive summary highlighting:
    1. Overall business performance
    2. Key achievements and concerns
    3. Strategic priorities for leadership attention

    Keep it professional and actionable for C-level executives.
    `;

    try {
      const result = await this.model.generateContent(prompt);
      const response = await result.response;
      return response.text();
    } catch (error) {
      console.error('Executive summary generation error:', error);
      return 'Executive summary generation temporarily unavailable. Please refer to detailed metrics and insights below.';
    }
  }

  /**
   * Generate strategic recommendations
   */
  private async generateStrategicRecommendations(analyticsResult: AnalyticsResult): Promise<string[]> {
    const highImpactInsights = analyticsResult.aiInsights.filter(insight => insight.impact === 'high');
    
    const recommendations = [
      'Focus on improving collection processes to reduce DSO',
      'Invest in supply chain optimization to enhance on-time delivery',
      'Implement cross-domain analytics for better decision making',
      'Strengthen customer relationship management practices'
    ];

    // Add AI-generated recommendations
    highImpactInsights.forEach(insight => {
      recommendations.push(...insight.recommendedActions);
    });

    return [...new Set(recommendations)].slice(0, 8); // Remove duplicates and limit
  }

  /**
   * Extract performance highlights
   */
  private extractPerformanceHighlights(analyticsResult: AnalyticsResult): string[] {
    const goodKPIs = analyticsResult.kpis.filter(kpi => kpi.status === 'good');
    const improvingTrends = analyticsResult.trends.filter(trend => trend.trend === 'increasing');
    
    const highlights = [
      ...goodKPIs.map(kpi => `${kpi.name} performing well at ${kpi.value}${kpi.unit}`),
      ...improvingTrends.map(trend => `${trend.metric} showing positive trend`)
    ];

    return highlights.slice(0, 5);
  }

  /**
   * Extract areas of concern
   */
  private extractAreasOfConcern(analyticsResult: AnalyticsResult): string[] {
    const criticalKPIs = analyticsResult.kpis.filter(kpi => kpi.status === 'critical');
    const warningKPIs = analyticsResult.kpis.filter(kpi => kpi.status === 'warning');
    const decliningTrends = analyticsResult.trends.filter(trend => trend.trend === 'decreasing');
    
    const concerns = [
      ...criticalKPIs.map(kpi => `${kpi.name} critically below target at ${kpi.value}${kpi.unit}`),
      ...warningKPIs.map(kpi => `${kpi.name} below target at ${kpi.value}${kpi.unit}`),
      ...decliningTrends.map(trend => `${trend.metric} showing declining trend`)
    ];

    return concerns.slice(0, 5);
  }

  /**
   * Generate next steps
   */
  private async generateNextSteps(analyticsResult: AnalyticsResult): Promise<string[]> {
    const urgentInsights = analyticsResult.aiInsights.filter(insight => insight.urgency === 'immediate');
    
    const nextSteps = [
      'Review and validate all critical KPIs',
      'Implement monitoring for declining trends',
      'Schedule cross-functional team meetings',
      'Develop action plans for underperforming areas'
    ];

    // Add urgent actions from AI insights
    urgentInsights.forEach(insight => {
      nextSteps.push(...insight.recommendedActions);
    });

    return [...new Set(nextSteps)].slice(0, 6);
  }

  /**
   * Format date range for display
   */
  private formatDateRange(timeframe: { start: Date; end: Date }): string {
    const formatter = new Intl.DateTimeFormat('en-US', { 
      year: 'numeric', 
      month: 'short', 
      day: 'numeric' 
    });
    return `${formatter.format(timeframe.start)} - ${formatter.format(timeframe.end)}`;
  }
}

export const advancedAnalyticsService = new AdvancedAnalyticsService();
