import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  User, Building, Phone, Mail, MapPin, Calendar, DollarSign,
  TrendingUp, TrendingDown, Package, Truck, AlertTriangle,
  CheckCircle, Clock, Star, MessageSquare, FileText,
  CreditCard, Ship, Target, Zap, Shield, Activity
} from 'lucide-react';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Bar<PERSON><PERSON>, Bar, <PERSON><PERSON>, <PERSON>, <PERSON> } from 'recharts';

interface Customer360Data {
  customer: CustomerProfile;
  arSummary: ARSummary;
  supplyChainSummary: SupplyChainSummary;
  interactions: CustomerInteraction[];
  riskAssessment: RiskAssessment;
  performanceMetrics: PerformanceMetrics;
  crossDomainInsights: CrossDomainInsights;
}

interface CustomerProfile {
  id: string;
  name: string;
  type: 'enterprise' | 'mid_market' | 'small_business';
  priority: 'critical' | 'high' | 'medium' | 'low';
  industry: string;
  establishedDate: Date;
  primaryContact: {
    name: string;
    email: string;
    phone: string;
    title: string;
  };
  address: {
    street: string;
    city: string;
    state: string;
    country: string;
    postalCode: string;
  };
  relationshipManager: string;
  tags: string[];
  status: 'active' | 'inactive' | 'suspended';
}

interface ARSummary {
  totalOutstanding: number;
  overdueAmount: number;
  currentAmount: number;
  averagePaymentDays: number;
  paymentReliability: number;
  creditLimit: number;
  creditUtilization: number;
  lastPaymentDate: Date;
  lastPaymentAmount: number;
  disputeCount: number;
  totalLifetimeValue: number;
}

interface SupplyChainSummary {
  activeShipments: number;
  pendingOrders: number;
  totalShipmentsYTD: number;
  onTimeDeliveryRate: number;
  averageTransitTime: number;
  exceptionRate: number;
  totalShipmentValue: number;
  preferredCarriers: string[];
  lastShipmentDate: Date;
  nextExpectedDelivery: Date;
}

interface CustomerInteraction {
  id: string;
  type: 'email' | 'call' | 'meeting' | 'support' | 'payment' | 'shipment';
  date: Date;
  subject: string;
  description: string;
  outcome: string;
  sentiment: 'positive' | 'neutral' | 'negative';
  createdBy: string;
  domain: 'ar' | 'supply_chain' | 'sales' | 'support';
}

interface RiskAssessment {
  overallScore: number;
  level: 'low' | 'medium' | 'high' | 'critical';
  factors: RiskFactor[];
  recommendations: string[];
  lastUpdated: Date;
}

interface RiskFactor {
  category: string;
  description: string;
  impact: 'low' | 'medium' | 'high';
  trend: 'improving' | 'stable' | 'declining';
}

interface PerformanceMetrics {
  paymentTrend: { month: string; amount: number; onTime: number }[];
  shipmentTrend: { month: string; shipments: number; onTime: number; value: number }[];
  satisfactionScore: number;
  npsScore: number;
  retentionProbability: number;
}

interface CrossDomainInsights {
  aiRecommendations: string[];
  opportunityScore: number;
  relationshipHealth: number;
  nextBestActions: string[];
  predictedIssues: string[];
  strategicValue: 'high' | 'medium' | 'low';
}

interface Customer360ViewProps {
  customerId: string;
}

export const Customer360View: React.FC<Customer360ViewProps> = ({ customerId }) => {
  const [data, setData] = useState<Customer360Data | null>(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('overview');

  useEffect(() => {
    loadCustomerData();
  }, [customerId]);

  const loadCustomerData = async () => {
    setLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      const mockData: Customer360Data = {
        customer: {
          id: customerId,
          name: 'Acme Corporation',
          type: 'enterprise',
          priority: 'high',
          industry: 'Manufacturing',
          establishedDate: new Date('2018-03-15'),
          primaryContact: {
            name: 'John Smith',
            email: '<EMAIL>',
            phone: '+****************',
            title: 'Procurement Director'
          },
          address: {
            street: '123 Business Ave',
            city: 'New York',
            state: 'NY',
            country: 'USA',
            postalCode: '10001'
          },
          relationshipManager: 'Sarah Johnson',
          tags: ['Key Account', 'Manufacturing', 'High Volume'],
          status: 'active'
        },
        arSummary: {
          totalOutstanding: 245000,
          overdueAmount: 75000,
          currentAmount: 170000,
          averagePaymentDays: 35,
          paymentReliability: 85,
          creditLimit: 500000,
          creditUtilization: 49,
          lastPaymentDate: new Date('2024-01-20'),
          lastPaymentAmount: 125000,
          disputeCount: 2,
          totalLifetimeValue: 3200000
        },
        supplyChainSummary: {
          activeShipments: 8,
          pendingOrders: 3,
          totalShipmentsYTD: 156,
          onTimeDeliveryRate: 92,
          averageTransitTime: 18,
          exceptionRate: 8,
          totalShipmentValue: 2800000,
          preferredCarriers: ['FedEx', 'UPS', 'DHL'],
          lastShipmentDate: new Date('2024-02-01'),
          nextExpectedDelivery: new Date('2024-02-15')
        },
        interactions: [
          {
            id: '1',
            type: 'email',
            date: new Date('2024-02-05'),
            subject: 'Payment Reminder - Invoice #12345',
            description: 'Sent automated payment reminder for overdue invoice',
            outcome: 'Payment commitment received',
            sentiment: 'neutral',
            createdBy: 'AR System',
            domain: 'ar'
          },
          {
            id: '2',
            type: 'call',
            date: new Date('2024-02-03'),
            subject: 'Shipment Delay Discussion',
            description: 'Discussed container delay due to port congestion',
            outcome: 'Customer understanding, expedited alternative arranged',
            sentiment: 'positive',
            createdBy: 'Mike Chen',
            domain: 'supply_chain'
          },
          {
            id: '3',
            type: 'meeting',
            date: new Date('2024-01-28'),
            subject: 'Quarterly Business Review',
            description: 'Reviewed performance metrics and upcoming projects',
            outcome: 'Renewed contract for additional $500K',
            sentiment: 'positive',
            createdBy: 'Sarah Johnson',
            domain: 'sales'
          }
        ],
        riskAssessment: {
          overallScore: 25,
          level: 'low',
          factors: [
            {
              category: 'Payment History',
              description: 'Consistent payment pattern with occasional delays',
              impact: 'medium',
              trend: 'stable'
            },
            {
              category: 'Business Stability',
              description: 'Strong financial position and market presence',
              impact: 'low',
              trend: 'improving'
            }
          ],
          recommendations: [
            'Monitor payment patterns for Q1',
            'Consider credit limit increase based on growth',
            'Maintain regular communication'
          ],
          lastUpdated: new Date('2024-02-01')
        },
        performanceMetrics: {
          paymentTrend: [
            { month: 'Sep', amount: 180000, onTime: 85 },
            { month: 'Oct', amount: 220000, onTime: 90 },
            { month: 'Nov', amount: 195000, onTime: 80 },
            { month: 'Dec', amount: 275000, onTime: 88 },
            { month: 'Jan', amount: 245000, onTime: 75 },
            { month: 'Feb', amount: 170000, onTime: 85 }
          ],
          shipmentTrend: [
            { month: 'Sep', shipments: 24, onTime: 95, value: 480000 },
            { month: 'Oct', shipments: 28, onTime: 89, value: 560000 },
            { month: 'Nov', shipments: 22, onTime: 91, value: 440000 },
            { month: 'Dec', shipments: 31, onTime: 87, value: 620000 },
            { month: 'Jan', shipments: 26, onTime: 92, value: 520000 },
            { month: 'Feb', shipments: 18, onTime: 94, value: 360000 }
          ],
          satisfactionScore: 4.2,
          npsScore: 8,
          retentionProbability: 92
        },
        crossDomainInsights: {
          aiRecommendations: [
            'Consider offering extended payment terms to improve cash flow',
            'Proactive communication about Q1 shipment schedules',
            'Opportunity for premium service tier upgrade'
          ],
          opportunityScore: 78,
          relationshipHealth: 85,
          nextBestActions: [
            'Schedule quarterly business review',
            'Discuss supply chain optimization',
            'Present new service offerings'
          ],
          predictedIssues: [
            'Potential Q1 cash flow constraints',
            'Increased shipping volume may strain capacity'
          ],
          strategicValue: 'high'
        }
      };

      setData(mockData);
    } catch (error) {
      console.error('Failed to load customer data:', error);
    } finally {
      setLoading(false);
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'critical': return 'text-red-600 bg-red-50 border-red-200';
      case 'high': return 'text-orange-600 bg-orange-50 border-orange-200';
      case 'medium': return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'low': return 'text-green-600 bg-green-50 border-green-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getRiskColor = (level: string) => {
    switch (level) {
      case 'critical': return 'text-red-600';
      case 'high': return 'text-orange-600';
      case 'medium': return 'text-yellow-600';
      case 'low': return 'text-green-600';
      default: return 'text-gray-600';
    }
  };

  const getSentimentIcon = (sentiment: string) => {
    switch (sentiment) {
      case 'positive': return <CheckCircle className="w-4 h-4 text-green-600" />;
      case 'negative': return <AlertTriangle className="w-4 h-4 text-red-600" />;
      default: return <Clock className="w-4 h-4 text-gray-600" />;
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    }).format(date);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="mt-2 text-muted-foreground">Loading customer profile...</p>
        </div>
      </div>
    );
  }

  if (!data) {
    return (
      <div className="text-center py-8">
        <p className="text-muted-foreground">Customer data not found</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Customer Header */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex items-start justify-between">
            <div className="flex items-start gap-4">
              <Avatar className="h-16 w-16">
                <AvatarImage src={`https://api.dicebear.com/7.x/initials/svg?seed=${data.customer.name}`} />
                <AvatarFallback>
                  {data.customer.name.split(' ').map(n => n[0]).join('')}
                </AvatarFallback>
              </Avatar>
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <h1 className="text-2xl font-bold">{data.customer.name}</h1>
                  <Badge className={getPriorityColor(data.customer.priority)}>
                    {data.customer.priority.charAt(0).toUpperCase() + data.customer.priority.slice(1)} Priority
                  </Badge>
                  <Badge variant="outline">{data.customer.type.replace('_', ' ')}</Badge>
                </div>
                <div className="flex items-center gap-4 text-sm text-muted-foreground">
                  <div className="flex items-center gap-1">
                    <Building className="w-4 h-4" />
                    {data.customer.industry}
                  </div>
                  <div className="flex items-center gap-1">
                    <User className="w-4 h-4" />
                    {data.customer.relationshipManager}
                  </div>
                  <div className="flex items-center gap-1">
                    <Calendar className="w-4 h-4" />
                    Customer since {formatDate(data.customer.establishedDate)}
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  {data.customer.tags.map(tag => (
                    <Badge key={tag} variant="secondary">{tag}</Badge>
                  ))}
                </div>
              </div>
            </div>
            <div className="text-right space-y-2">
              <div className="text-2xl font-bold text-green-600">
                {formatCurrency(data.arSummary.totalLifetimeValue)}
              </div>
              <div className="text-sm text-muted-foreground">Lifetime Value</div>
              <div className="flex items-center gap-2">
                <div className={`text-lg font-semibold ${getRiskColor(data.riskAssessment.level)}`}>
                  Risk: {data.riskAssessment.level.charAt(0).toUpperCase() + data.riskAssessment.level.slice(1)}
                </div>
                <div className="text-sm text-muted-foreground">
                  ({data.riskAssessment.overallScore}/100)
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Key Metrics Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Outstanding Balance</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(data.arSummary.totalOutstanding)}</div>
            <p className="text-xs text-muted-foreground">
              {formatCurrency(data.arSummary.overdueAmount)} overdue
            </p>
            <Progress
              value={data.arSummary.creditUtilization}
              className="mt-2"
            />
            <p className="text-xs text-muted-foreground mt-1">
              {data.arSummary.creditUtilization}% of credit limit
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Shipments</CardTitle>
            <Ship className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.supplyChainSummary.activeShipments}</div>
            <p className="text-xs text-muted-foreground">
              {data.supplyChainSummary.pendingOrders} pending orders
            </p>
            <div className="flex items-center gap-1 mt-2">
              <CheckCircle className="w-3 h-3 text-green-600" />
              <span className="text-xs text-green-600">
                {data.supplyChainSummary.onTimeDeliveryRate}% on-time delivery
              </span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Relationship Health</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {data.crossDomainInsights.relationshipHealth}%
            </div>
            <p className="text-xs text-muted-foreground">
              NPS Score: {data.performanceMetrics.npsScore}/10
            </p>
            <div className="flex items-center gap-1 mt-2">
              <Star className="w-3 h-3 text-yellow-500 fill-current" />
              <span className="text-xs">
                {data.performanceMetrics.satisfactionScore}/5.0 satisfaction
              </span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Opportunity Score</CardTitle>
            <Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">
              {data.crossDomainInsights.opportunityScore}%
            </div>
            <p className="text-xs text-muted-foreground">
              {data.crossDomainInsights.strategicValue} strategic value
            </p>
            <div className="flex items-center gap-1 mt-2">
              <TrendingUp className="w-3 h-3 text-green-600" />
              <span className="text-xs text-green-600">
                {data.performanceMetrics.retentionProbability}% retention probability
              </span>
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="ar">AR Details</TabsTrigger>
          <TabsTrigger value="supply-chain">Supply Chain</TabsTrigger>
          <TabsTrigger value="interactions">Interactions</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
          <TabsTrigger value="insights">AI Insights</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Contact Information */}
            <Card>
              <CardHeader>
                <CardTitle>Contact Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <User className="w-4 h-4 text-muted-foreground" />
                    <span className="font-medium">{data.customer.primaryContact.name}</span>
                    <Badge variant="outline">{data.customer.primaryContact.title}</Badge>
                  </div>
                  <div className="flex items-center gap-2">
                    <Mail className="w-4 h-4 text-muted-foreground" />
                    <span>{data.customer.primaryContact.email}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Phone className="w-4 h-4 text-muted-foreground" />
                    <span>{data.customer.primaryContact.phone}</span>
                  </div>
                  <div className="flex items-start gap-2">
                    <MapPin className="w-4 h-4 text-muted-foreground mt-0.5" />
                    <div>
                      <div>{data.customer.address.street}</div>
                      <div>{data.customer.address.city}, {data.customer.address.state} {data.customer.address.postalCode}</div>
                      <div>{data.customer.address.country}</div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Risk Assessment */}
            <Card>
              <CardHeader>
                <CardTitle>Risk Assessment</CardTitle>
                <CardDescription>Last updated: {formatDate(data.riskAssessment.lastUpdated)}</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span>Overall Risk Score</span>
                    <div className="flex items-center gap-2">
                      <Progress value={data.riskAssessment.overallScore} className="w-20" />
                      <span className={`font-bold ${getRiskColor(data.riskAssessment.level)}`}>
                        {data.riskAssessment.overallScore}/100
                      </span>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <h4 className="font-medium">Risk Factors</h4>
                    {data.riskAssessment.factors.map((factor, index) => (
                      <div key={index} className="flex items-center justify-between p-2 border rounded">
                        <div>
                          <div className="font-medium text-sm">{factor.category}</div>
                          <div className="text-xs text-muted-foreground">{factor.description}</div>
                        </div>
                        <div className="flex items-center gap-2">
                          <Badge variant={factor.impact === 'high' ? 'destructive' : factor.impact === 'medium' ? 'default' : 'secondary'}>
                            {factor.impact}
                          </Badge>
                          {factor.trend === 'improving' ? (
                            <TrendingUp className="w-4 h-4 text-green-600" />
                          ) : factor.trend === 'declining' ? (
                            <TrendingDown className="w-4 h-4 text-red-600" />
                          ) : (
                            <div className="w-4 h-4" />
                          )}
                        </div>
                      </div>
                    ))}
                  </div>

                  <div className="space-y-2">
                    <h4 className="font-medium">Recommendations</h4>
                    <ul className="space-y-1">
                      {data.riskAssessment.recommendations.map((rec, index) => (
                        <li key={index} className="text-sm text-muted-foreground flex items-start gap-2">
                          <CheckCircle className="w-3 h-3 text-green-600 mt-0.5 flex-shrink-0" />
                          {rec}
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Recent Activity */}
            <Card className="lg:col-span-2">
              <CardHeader>
                <CardTitle>Recent Activity</CardTitle>
                <CardDescription>Latest interactions across all domains</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {data.interactions.slice(0, 5).map((interaction) => (
                    <div key={interaction.id} className="flex items-start gap-3 p-3 border rounded-lg">
                      <div className="flex-shrink-0">
                        {getSentimentIcon(interaction.sentiment)}
                      </div>
                      <div className="flex-1 space-y-1">
                        <div className="flex items-center gap-2">
                          <span className="font-medium">{interaction.subject}</span>
                          <Badge variant="outline">{interaction.domain.replace('_', ' ')}</Badge>
                          <Badge variant="secondary">{interaction.type}</Badge>
                        </div>
                        <p className="text-sm text-muted-foreground">{interaction.description}</p>
                        <div className="flex items-center gap-4 text-xs text-muted-foreground">
                          <span>{formatDate(interaction.date)}</span>
                          <span>by {interaction.createdBy}</span>
                          <span className="text-green-600">Outcome: {interaction.outcome}</span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="ar" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* AR Summary */}
            <Card>
              <CardHeader>
                <CardTitle>Accounts Receivable Summary</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <div className="text-sm text-muted-foreground">Total Outstanding</div>
                    <div className="text-2xl font-bold">{formatCurrency(data.arSummary.totalOutstanding)}</div>
                  </div>
                  <div>
                    <div className="text-sm text-muted-foreground">Overdue Amount</div>
                    <div className="text-2xl font-bold text-red-600">{formatCurrency(data.arSummary.overdueAmount)}</div>
                  </div>
                  <div>
                    <div className="text-sm text-muted-foreground">Current Amount</div>
                    <div className="text-2xl font-bold text-green-600">{formatCurrency(data.arSummary.currentAmount)}</div>
                  </div>
                  <div>
                    <div className="text-sm text-muted-foreground">Credit Utilization</div>
                    <div className="text-2xl font-bold">{data.arSummary.creditUtilization}%</div>
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span>Credit Limit</span>
                    <span className="font-medium">{formatCurrency(data.arSummary.creditLimit)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Average Payment Days</span>
                    <span className="font-medium">{data.arSummary.averagePaymentDays} days</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Payment Reliability</span>
                    <span className="font-medium">{data.arSummary.paymentReliability}%</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Last Payment</span>
                    <span className="font-medium">
                      {formatCurrency(data.arSummary.lastPaymentAmount)} on {formatDate(data.arSummary.lastPaymentDate)}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Payment Trend */}
            <Card>
              <CardHeader>
                <CardTitle>Payment Trend</CardTitle>
                <CardDescription>Monthly payment amounts and on-time performance</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <LineChart data={data.performanceMetrics.paymentTrend}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis yAxisId="left" />
                    <YAxis yAxisId="right" orientation="right" />
                    <Tooltip
                      formatter={(value, name) => [
                        name === 'amount' ? formatCurrency(value as number) : `${value}%`,
                        name === 'amount' ? 'Payment Amount' : 'On-Time %'
                      ]}
                    />
                    <Bar yAxisId="left" dataKey="amount" fill="#3b82f6" name="amount" />
                    <Line yAxisId="right" type="monotone" dataKey="onTime" stroke="#22c55e" strokeWidth={2} name="onTime" />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="supply-chain" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Supply Chain Summary */}
            <Card>
              <CardHeader>
                <CardTitle>Supply Chain Summary</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <div className="text-sm text-muted-foreground">Active Shipments</div>
                    <div className="text-2xl font-bold">{data.supplyChainSummary.activeShipments}</div>
                  </div>
                  <div>
                    <div className="text-sm text-muted-foreground">Pending Orders</div>
                    <div className="text-2xl font-bold">{data.supplyChainSummary.pendingOrders}</div>
                  </div>
                  <div>
                    <div className="text-sm text-muted-foreground">YTD Shipments</div>
                    <div className="text-2xl font-bold">{data.supplyChainSummary.totalShipmentsYTD}</div>
                  </div>
                  <div>
                    <div className="text-sm text-muted-foreground">On-Time Rate</div>
                    <div className="text-2xl font-bold text-green-600">{data.supplyChainSummary.onTimeDeliveryRate}%</div>
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span>Average Transit Time</span>
                    <span className="font-medium">{data.supplyChainSummary.averageTransitTime} days</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Exception Rate</span>
                    <span className="font-medium">{data.supplyChainSummary.exceptionRate}%</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Total Shipment Value</span>
                    <span className="font-medium">{formatCurrency(data.supplyChainSummary.totalShipmentValue)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Last Shipment</span>
                    <span className="font-medium">{formatDate(data.supplyChainSummary.lastShipmentDate)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Next Expected Delivery</span>
                    <span className="font-medium">{formatDate(data.supplyChainSummary.nextExpectedDelivery)}</span>
                  </div>
                </div>

                <div>
                  <div className="text-sm text-muted-foreground mb-2">Preferred Carriers</div>
                  <div className="flex gap-2">
                    {data.supplyChainSummary.preferredCarriers.map(carrier => (
                      <Badge key={carrier} variant="outline">{carrier}</Badge>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Shipment Trend */}
            <Card>
              <CardHeader>
                <CardTitle>Shipment Performance</CardTitle>
                <CardDescription>Monthly shipment volume and on-time delivery</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <LineChart data={data.performanceMetrics.shipmentTrend}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis yAxisId="left" />
                    <YAxis yAxisId="right" orientation="right" />
                    <Tooltip />
                    <Bar yAxisId="left" dataKey="shipments" fill="#8b5cf6" name="Shipments" />
                    <Line yAxisId="right" type="monotone" dataKey="onTime" stroke="#22c55e" strokeWidth={2} name="On-Time %" />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="interactions" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Interaction Timeline</CardTitle>
              <CardDescription>Complete history of customer interactions across all domains</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {data.interactions.map((interaction) => (
                  <div key={interaction.id} className="flex items-start gap-4 p-4 border rounded-lg">
                    <div className="flex-shrink-0">
                      {interaction.type === 'email' && <Mail className="w-5 h-5 text-blue-600" />}
                      {interaction.type === 'call' && <Phone className="w-5 h-5 text-green-600" />}
                      {interaction.type === 'meeting' && <Calendar className="w-5 h-5 text-purple-600" />}
                      {interaction.type === 'support' && <MessageSquare className="w-5 h-5 text-orange-600" />}
                      {interaction.type === 'payment' && <CreditCard className="w-5 h-5 text-green-600" />}
                      {interaction.type === 'shipment' && <Package className="w-5 h-5 text-blue-600" />}
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-2">
                        <h3 className="font-semibold">{interaction.subject}</h3>
                        <Badge variant="outline">{interaction.domain.replace('_', ' ')}</Badge>
                        <Badge variant="secondary">{interaction.type}</Badge>
                        {getSentimentIcon(interaction.sentiment)}
                      </div>
                      <p className="text-sm text-muted-foreground mb-2">{interaction.description}</p>
                      <div className="flex items-center gap-4 text-xs text-muted-foreground">
                        <span>{formatDate(interaction.date)}</span>
                        <span>by {interaction.createdBy}</span>
                        <span className="text-green-600">Outcome: {interaction.outcome}</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Performance Metrics */}
            <Card>
              <CardHeader>
                <CardTitle>Performance Metrics</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span>Customer Satisfaction</span>
                    <div className="flex items-center gap-2">
                      <Progress value={data.performanceMetrics.satisfactionScore * 20} className="w-20" />
                      <span className="font-bold">{data.performanceMetrics.satisfactionScore}/5.0</span>
                    </div>
                  </div>
                  <div className="flex justify-between items-center">
                    <span>NPS Score</span>
                    <div className="flex items-center gap-2">
                      <Progress value={(data.performanceMetrics.npsScore + 10) * 5} className="w-20" />
                      <span className="font-bold">{data.performanceMetrics.npsScore}/10</span>
                    </div>
                  </div>
                  <div className="flex justify-between items-center">
                    <span>Retention Probability</span>
                    <div className="flex items-center gap-2">
                      <Progress value={data.performanceMetrics.retentionProbability} className="w-20" />
                      <span className="font-bold">{data.performanceMetrics.retentionProbability}%</span>
                    </div>
                  </div>
                  <div className="flex justify-between items-center">
                    <span>Relationship Health</span>
                    <div className="flex items-center gap-2">
                      <Progress value={data.crossDomainInsights.relationshipHealth} className="w-20" />
                      <span className="font-bold">{data.crossDomainInsights.relationshipHealth}%</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Opportunity Analysis */}
            <Card>
              <CardHeader>
                <CardTitle>Opportunity Analysis</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="text-center">
                    <div className="text-3xl font-bold text-blue-600 mb-2">
                      {data.crossDomainInsights.opportunityScore}%
                    </div>
                    <div className="text-sm text-muted-foreground">Opportunity Score</div>
                    <Progress value={data.crossDomainInsights.opportunityScore} className="mt-2" />
                  </div>

                  <div className="space-y-2">
                    <h4 className="font-medium">Strategic Value</h4>
                    <Badge className={
                      data.crossDomainInsights.strategicValue === 'high' ? 'bg-green-100 text-green-800' :
                      data.crossDomainInsights.strategicValue === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                      'bg-gray-100 text-gray-800'
                    }>
                      {data.crossDomainInsights.strategicValue.charAt(0).toUpperCase() + data.crossDomainInsights.strategicValue.slice(1)} Value
                    </Badge>
                  </div>

                  <div className="space-y-2">
                    <h4 className="font-medium">Next Best Actions</h4>
                    <ul className="space-y-1">
                      {data.crossDomainInsights.nextBestActions.map((action, index) => (
                        <li key={index} className="text-sm flex items-start gap-2">
                          <Target className="w-3 h-3 text-blue-600 mt-0.5 flex-shrink-0" />
                          {action}
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="insights" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* AI Recommendations */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Zap className="w-5 h-5 text-yellow-600" />
                  AI Recommendations
                </CardTitle>
                <CardDescription>Intelligent insights based on cross-domain analysis</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {data.crossDomainInsights.aiRecommendations.map((recommendation, index) => (
                    <div key={index} className="p-3 border rounded-lg">
                      <div className="flex items-start gap-2">
                        <Zap className="w-4 h-4 text-yellow-600 mt-0.5 flex-shrink-0" />
                        <span className="text-sm">{recommendation}</span>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Predicted Issues */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <AlertTriangle className="w-5 h-5 text-orange-600" />
                  Predicted Issues
                </CardTitle>
                <CardDescription>Potential challenges identified by AI analysis</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {data.crossDomainInsights.predictedIssues.map((issue, index) => (
                    <Alert key={index}>
                      <AlertTriangle className="h-4 w-4" />
                      <AlertDescription>{issue}</AlertDescription>
                    </Alert>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Cross-Domain Analysis */}
          <Card>
            <CardHeader>
              <CardTitle>Cross-Domain Analysis</CardTitle>
              <CardDescription>Comprehensive view of customer relationship across all business domains</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="text-center p-4 border rounded-lg">
                  <DollarSign className="w-8 h-8 mx-auto mb-2 text-green-600" />
                  <div className="text-2xl font-bold">{formatCurrency(data.arSummary.totalLifetimeValue)}</div>
                  <div className="text-sm text-muted-foreground">Total Lifetime Value</div>
                </div>
                <div className="text-center p-4 border rounded-lg">
                  <Package className="w-8 h-8 mx-auto mb-2 text-blue-600" />
                  <div className="text-2xl font-bold">{data.supplyChainSummary.totalShipmentsYTD}</div>
                  <div className="text-sm text-muted-foreground">Shipments This Year</div>
                </div>
                <div className="text-center p-4 border rounded-lg">
                  <Shield className="w-8 h-8 mx-auto mb-2 text-purple-600" />
                  <div className="text-2xl font-bold">{data.riskAssessment.overallScore}/100</div>
                  <div className="text-sm text-muted-foreground">Risk Score</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};