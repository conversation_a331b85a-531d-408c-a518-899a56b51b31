import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  BarChart3,
  TrendingUp,
  TrendingDown,
  DollarSign,
  Users,
  FileText,
  Package,
  Calendar,
  Download,
  Filter,
  RefreshCw,
  AlertTriangle,
  CheckCircle,
  Clock,
  Target
} from 'lucide-react';

interface AnalyticsData {
  revenue: {
    current: number;
    previous: number;
    growth: number;
  };
  customers: {
    total: number;
    new: number;
    active: number;
  };
  invoices: {
    total: number;
    paid: number;
    overdue: number;
    pending: number;
  };
  collections: {
    rate: number;
    avgDays: number;
    totalCollected: number;
  };
  topCustomers: Array<{
    id: string;
    name: string;
    revenue: number;
    growth: number;
  }>;
  monthlyRevenue: Array<{
    month: string;
    revenue: number;
    invoices: number;
  }>;
  paymentTrends: Array<{
    period: string;
    onTime: number;
    late: number;
    overdue: number;
  }>;
}

// Mock analytics data
const mockAnalyticsData: AnalyticsData = {
  revenue: {
    current: 485000,
    previous: 420000,
    growth: 15.5
  },
  customers: {
    total: 156,
    new: 12,
    active: 134
  },
  invoices: {
    total: 89,
    paid: 67,
    overdue: 8,
    pending: 14
  },
  collections: {
    rate: 92.5,
    avgDays: 28,
    totalCollected: 448750
  },
  topCustomers: [
    { id: '1', name: 'Acme Corporation', revenue: 125000, growth: 12.5 },
    { id: '2', name: 'TechStart Inc', revenue: 98000, growth: 8.2 },
    { id: '3', name: 'Global Solutions LLC', revenue: 87500, growth: -2.1 },
    { id: '4', name: 'Innovation Labs', revenue: 76000, growth: 22.3 },
    { id: '5', name: 'Digital Dynamics', revenue: 65000, growth: 5.7 }
  ],
  monthlyRevenue: [
    { month: 'Jan', revenue: 65000, invoices: 12 },
    { month: 'Feb', revenue: 72000, invoices: 15 },
    { month: 'Mar', revenue: 68000, invoices: 13 },
    { month: 'Apr', revenue: 85000, invoices: 18 },
    { month: 'May', revenue: 92000, invoices: 16 },
    { month: 'Jun', revenue: 103000, invoices: 15 }
  ],
  paymentTrends: [
    { period: 'Q1 2024', onTime: 75, late: 15, overdue: 10 },
    { period: 'Q2 2024', onTime: 82, late: 12, overdue: 6 },
    { period: 'Q3 2024', onTime: 78, late: 14, overdue: 8 },
    { period: 'Q4 2024', onTime: 85, late: 10, overdue: 5 }
  ]
};

export const AnalyticsPage: React.FC = () => {
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(null);
  const [selectedPeriod, setSelectedPeriod] = useState('current-quarter');
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    // Simulate API call
    setAnalyticsData(mockAnalyticsData);
  }, []);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  const formatPercentage = (value: number) => {
    return `${value > 0 ? '+' : ''}${value.toFixed(1)}%`;
  };

  const refreshData = () => {
    setIsLoading(true);
    // Simulate API refresh
    setTimeout(() => {
      setIsLoading(false);
    }, 1000);
  };

  if (!analyticsData) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <BarChart3 className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <p className="text-muted-foreground">Loading analytics...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Analytics Dashboard</h2>
          <p className="text-muted-foreground">
            Business intelligence and performance metrics
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <select
            value={selectedPeriod}
            onChange={(e) => setSelectedPeriod(e.target.value)}
            className="px-3 py-2 border border-input bg-background rounded-md text-sm"
          >
            <option value="current-quarter">Current Quarter</option>
            <option value="last-quarter">Last Quarter</option>
            <option value="year-to-date">Year to Date</option>
            <option value="last-year">Last Year</option>
          </select>
          <Button variant="outline" onClick={refreshData} disabled={isLoading}>
            <RefreshCw className={`mr-2 h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button variant="outline">
            <Download className="mr-2 h-4 w-4" />
            Export
          </Button>
        </div>
      </div>

      {/* Key Performance Indicators */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(analyticsData.revenue.current)}</div>
            <div className="flex items-center text-xs">
              {analyticsData.revenue.growth > 0 ? (
                <TrendingUp className="h-3 w-3 text-green-600 mr-1" />
              ) : (
                <TrendingDown className="h-3 w-3 text-red-600 mr-1" />
              )}
              <span className={analyticsData.revenue.growth > 0 ? 'text-green-600' : 'text-red-600'}>
                {formatPercentage(analyticsData.revenue.growth)}
              </span>
              <span className="text-muted-foreground ml-1">from last period</span>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Customers</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{analyticsData.customers.active}</div>
            <p className="text-xs text-muted-foreground">
              {analyticsData.customers.new} new this period
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Collection Rate</CardTitle>
            <Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{analyticsData.collections.rate}%</div>
            <p className="text-xs text-muted-foreground">
              {formatCurrency(analyticsData.collections.totalCollected)} collected
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Collection Days</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{analyticsData.collections.avgDays}</div>
            <p className="text-xs text-muted-foreground">
              {analyticsData.collections.avgDays <= 30 ? 'Within terms' : 'Above target'}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Invoice Status Overview */}
      <Card>
        <CardHeader>
          <CardTitle>Invoice Status Overview</CardTitle>
          <CardDescription>
            Current status of all invoices in the system
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-4">
            <div className="flex items-center space-x-3 p-4 border rounded-lg">
              <CheckCircle className="h-8 w-8 text-green-600" />
              <div>
                <p className="text-2xl font-bold">{analyticsData.invoices.paid}</p>
                <p className="text-sm text-muted-foreground">Paid Invoices</p>
              </div>
            </div>
            <div className="flex items-center space-x-3 p-4 border rounded-lg">
              <Clock className="h-8 w-8 text-yellow-600" />
              <div>
                <p className="text-2xl font-bold">{analyticsData.invoices.pending}</p>
                <p className="text-sm text-muted-foreground">Pending</p>
              </div>
            </div>
            <div className="flex items-center space-x-3 p-4 border rounded-lg">
              <AlertTriangle className="h-8 w-8 text-red-600" />
              <div>
                <p className="text-2xl font-bold">{analyticsData.invoices.overdue}</p>
                <p className="text-sm text-muted-foreground">Overdue</p>
              </div>
            </div>
            <div className="flex items-center space-x-3 p-4 border rounded-lg">
              <FileText className="h-8 w-8 text-gray-600" />
              <div>
                <p className="text-2xl font-bold">{analyticsData.invoices.total}</p>
                <p className="text-sm text-muted-foreground">Total Invoices</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Revenue Trends and Top Customers */}
      <div className="grid gap-6 lg:grid-cols-2">
        {/* Monthly Revenue Trend */}
        <Card>
          <CardHeader>
            <CardTitle>Monthly Revenue Trend</CardTitle>
            <CardDescription>
              Revenue performance over the last 6 months
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {analyticsData.monthlyRevenue.map((month, index) => {
                const maxRevenue = Math.max(...analyticsData.monthlyRevenue.map(m => m.revenue));
                const widthPercentage = (month.revenue / maxRevenue) * 100;

                return (
                  <div key={month.month} className="space-y-2">
                    <div className="flex items-center justify-between text-sm">
                      <span className="font-medium">{month.month}</span>
                      <span className="text-muted-foreground">{month.invoices} invoices</span>
                    </div>
                    <div className="flex items-center space-x-3">
                      <div className="flex-1 bg-muted rounded-full h-2">
                        <div
                          className="bg-primary h-2 rounded-full transition-all duration-300"
                          style={{ width: `${widthPercentage}%` }}
                        />
                      </div>
                      <span className="text-sm font-medium min-w-[80px] text-right">
                        {formatCurrency(month.revenue)}
                      </span>
                    </div>
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>

        {/* Top Customers */}
        <Card>
          <CardHeader>
            <CardTitle>Top Customers by Revenue</CardTitle>
            <CardDescription>
              Highest revenue generating customers this period
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {analyticsData.topCustomers.map((customer, index) => (
                <div key={customer.id} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center space-x-3">
                    <div className="flex items-center justify-center w-8 h-8 bg-primary/10 rounded-full">
                      <span className="text-sm font-medium">#{index + 1}</span>
                    </div>
                    <div>
                      <p className="font-medium">{customer.name}</p>
                      <div className="flex items-center space-x-2">
                        <span className="text-sm text-muted-foreground">
                          {formatCurrency(customer.revenue)}
                        </span>
                        <Badge
                          variant={customer.growth > 0 ? "default" : "secondary"}
                          className={customer.growth > 0 ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"}
                        >
                          {formatPercentage(customer.growth)}
                        </Badge>
                      </div>
                    </div>
                  </div>
                  {customer.growth > 0 ? (
                    <TrendingUp className="h-4 w-4 text-green-600" />
                  ) : (
                    <TrendingDown className="h-4 w-4 text-red-600" />
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Payment Trends */}
      <Card>
        <CardHeader>
          <CardTitle>Payment Performance Trends</CardTitle>
          <CardDescription>
            Payment timing analysis across quarters
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            {analyticsData.paymentTrends.map((trend) => (
              <div key={trend.period} className="space-y-3">
                <div className="flex items-center justify-between">
                  <h4 className="font-medium">{trend.period}</h4>
                  <div className="text-sm text-muted-foreground">
                    Total: {trend.onTime + trend.late + trend.overdue} payments
                  </div>
                </div>
                <div className="grid gap-3 md:grid-cols-3">
                  <div className="flex items-center space-x-3 p-3 bg-green-50 rounded-lg">
                    <CheckCircle className="h-5 w-5 text-green-600" />
                    <div>
                      <p className="text-lg font-semibold">{trend.onTime}</p>
                      <p className="text-sm text-green-700">On Time</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3 p-3 bg-yellow-50 rounded-lg">
                    <Clock className="h-5 w-5 text-yellow-600" />
                    <div>
                      <p className="text-lg font-semibold">{trend.late}</p>
                      <p className="text-sm text-yellow-700">Late</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3 p-3 bg-red-50 rounded-lg">
                    <AlertTriangle className="h-5 w-5 text-red-600" />
                    <div>
                      <p className="text-lg font-semibold">{trend.overdue}</p>
                      <p className="text-sm text-red-700">Overdue</p>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};