"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Direction = exports.CommunicationType = exports.ExceptionStatus = exports.Severity = exports.ExceptionType = exports.RuleDomain = exports.DocumentType = exports.ShipmentStatus = exports.PaymentMethod = exports.InvoiceStatus = exports.Priority = void 0;
// Enums
var Priority;
(function (Priority) {
    Priority["LOW"] = "LOW";
    Priority["NORMAL"] = "NORMAL";
    Priority["HIGH"] = "HIGH";
    Priority["CRITICAL"] = "CRITICAL";
})(Priority || (exports.Priority = Priority = {}));
var InvoiceStatus;
(function (InvoiceStatus) {
    InvoiceStatus["PENDING"] = "PENDING";
    InvoiceStatus["OVERDUE"] = "OVERDUE";
    InvoiceStatus["PAID"] = "PAID";
    InvoiceStatus["CANCELLED"] = "CANCELLED";
})(InvoiceStatus || (exports.InvoiceStatus = InvoiceStatus = {}));
var PaymentMethod;
(function (PaymentMethod) {
    PaymentMethod["BANK_TRANSFER"] = "BANK_TRANSFER";
    PaymentMethod["CREDIT_CARD"] = "CREDIT_CARD";
    PaymentMethod["CHECK"] = "CHECK";
    PaymentMethod["CASH"] = "CASH";
    PaymentMethod["OTHER"] = "OTHER";
})(PaymentMethod || (exports.PaymentMethod = PaymentMethod = {}));
var ShipmentStatus;
(function (ShipmentStatus) {
    ShipmentStatus["PENDING"] = "PENDING";
    ShipmentStatus["IN_TRANSIT"] = "IN_TRANSIT";
    ShipmentStatus["DELIVERED"] = "DELIVERED";
    ShipmentStatus["DELAYED"] = "DELAYED";
    ShipmentStatus["CANCELLED"] = "CANCELLED";
})(ShipmentStatus || (exports.ShipmentStatus = ShipmentStatus = {}));
var DocumentType;
(function (DocumentType) {
    DocumentType["PACKING_LIST"] = "PACKING_LIST";
    DocumentType["SHIPPING_NOTICE"] = "SHIPPING_NOTICE";
    DocumentType["INVOICE"] = "INVOICE";
    DocumentType["RECEIPT"] = "RECEIPT";
    DocumentType["OTHER"] = "OTHER";
})(DocumentType || (exports.DocumentType = DocumentType = {}));
var RuleDomain;
(function (RuleDomain) {
    RuleDomain["AR"] = "AR";
    RuleDomain["SUPPLY_CHAIN"] = "SUPPLY_CHAIN";
    RuleDomain["CROSS_DOMAIN"] = "CROSS_DOMAIN";
})(RuleDomain || (exports.RuleDomain = RuleDomain = {}));
var ExceptionType;
(function (ExceptionType) {
    ExceptionType["PAYMENT_OVERDUE"] = "PAYMENT_OVERDUE";
    ExceptionType["SHIPMENT_DELAY"] = "SHIPMENT_DELAY";
    ExceptionType["MISSING_DOCUMENT"] = "MISSING_DOCUMENT";
    ExceptionType["RULE_VIOLATION"] = "RULE_VIOLATION";
    ExceptionType["SYSTEM_ERROR"] = "SYSTEM_ERROR";
})(ExceptionType || (exports.ExceptionType = ExceptionType = {}));
var Severity;
(function (Severity) {
    Severity["LOW"] = "LOW";
    Severity["MEDIUM"] = "MEDIUM";
    Severity["HIGH"] = "HIGH";
    Severity["CRITICAL"] = "CRITICAL";
})(Severity || (exports.Severity = Severity = {}));
var ExceptionStatus;
(function (ExceptionStatus) {
    ExceptionStatus["OPEN"] = "OPEN";
    ExceptionStatus["IN_PROGRESS"] = "IN_PROGRESS";
    ExceptionStatus["RESOLVED"] = "RESOLVED";
    ExceptionStatus["CLOSED"] = "CLOSED";
})(ExceptionStatus || (exports.ExceptionStatus = ExceptionStatus = {}));
var CommunicationType;
(function (CommunicationType) {
    CommunicationType["EMAIL"] = "EMAIL";
    CommunicationType["SMS"] = "SMS";
    CommunicationType["PHONE"] = "PHONE";
    CommunicationType["SYSTEM"] = "SYSTEM";
})(CommunicationType || (exports.CommunicationType = CommunicationType = {}));
var Direction;
(function (Direction) {
    Direction["INBOUND"] = "INBOUND";
    Direction["OUTBOUND"] = "OUTBOUND";
})(Direction || (exports.Direction = Direction = {}));
