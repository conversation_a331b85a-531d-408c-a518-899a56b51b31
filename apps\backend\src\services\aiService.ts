import { GoogleGenerativeAI } from '@google/generative-ai';
import { config } from '@/config/config';
import { AppError } from '@ar-scia/shared-utils';

interface DocumentAnalysisResult {
  summary: string;
  keyPoints: string[];
  entities: {
    customers: string[];
    amounts: string[];
    dates: string[];
    products: string[];
  };
  sentiment: 'positive' | 'neutral' | 'negative';
  confidence: number;
}

interface BusinessRuleEvaluation {
  ruleName: string;
  passed: boolean;
  confidence: number;
  explanation: string;
  recommendations?: string[];
}

interface InvoiceAnalysis {
  totalAmount: number;
  lineItems: Array<{
    description: string;
    quantity: number;
    unitPrice: number;
    total: number;
  }>;
  dueDate: string;
  customerInfo: {
    name: string;
    address?: string;
    email?: string;
  };
  confidence: number;
}

export class AIService {
  private genAI: GoogleGenerativeAI;
  private model: any;

  constructor() {
    if (!config.geminiApiKey) {
      console.warn('Gemini API key not configured. AI features will be disabled.');
      return;
    }

    this.genAI = new GoogleGenerativeAI(config.geminiApiKey);
    this.model = this.genAI.getGenerativeModel({ model: 'gemini-pro' });
  }

  private isConfigured(): boolean {
    return !!this.genAI && !!this.model;
  }

  async analyzeDocument(content: string, documentType: string = 'general'): Promise<DocumentAnalysisResult> {
    if (!this.isConfigured()) {
      throw new AppError('AI service not configured', 503);
    }

    try {
      const prompt = `
        Analyze the following ${documentType} document and provide a structured analysis:

        Document Content:
        ${content}

        Please provide:
        1. A concise summary (2-3 sentences)
        2. Key points (3-5 bullet points)
        3. Extract entities:
           - Customer names
           - Monetary amounts
           - Important dates
           - Products/services mentioned
        4. Overall sentiment (positive/neutral/negative)
        5. Confidence level (0-1)

        Respond in JSON format with the structure:
        {
          "summary": "...",
          "keyPoints": ["...", "..."],
          "entities": {
            "customers": ["..."],
            "amounts": ["..."],
            "dates": ["..."],
            "products": ["..."]
          },
          "sentiment": "positive|neutral|negative",
          "confidence": 0.95
        }
      `;

      const result = await this.model.generateContent(prompt);
      const response = await result.response;
      const text = response.text();

      // Parse JSON response
      const jsonMatch = text.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('Invalid AI response format');
      }

      const analysis = JSON.parse(jsonMatch[0]);
      return analysis;

    } catch (error) {
      console.error('Document analysis error:', error);
      throw new AppError('Failed to analyze document', 500);
    }
  }

  async evaluateBusinessRule(
    ruleDescription: string,
    data: Record<string, any>,
    context?: string
  ): Promise<BusinessRuleEvaluation> {
    if (!this.isConfigured()) {
      throw new AppError('AI service not configured', 503);
    }

    try {
      const prompt = `
        Evaluate the following business rule against the provided data:

        Business Rule: ${ruleDescription}
        
        Data to evaluate:
        ${JSON.stringify(data, null, 2)}
        
        ${context ? `Additional Context: ${context}` : ''}

        Please evaluate if the data satisfies the business rule and provide:
        1. Whether the rule passed (true/false)
        2. Confidence level (0-1)
        3. Clear explanation of the evaluation
        4. Recommendations if the rule failed

        Respond in JSON format:
        {
          "ruleName": "${ruleDescription}",
          "passed": true|false,
          "confidence": 0.95,
          "explanation": "...",
          "recommendations": ["...", "..."] // only if failed
        }
      `;

      const result = await this.model.generateContent(prompt);
      const response = await result.response;
      const text = response.text();

      const jsonMatch = text.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('Invalid AI response format');
      }

      const evaluation = JSON.parse(jsonMatch[0]);
      return evaluation;

    } catch (error) {
      console.error('Business rule evaluation error:', error);
      throw new AppError('Failed to evaluate business rule', 500);
    }
  }

  async analyzeInvoice(invoiceContent: string): Promise<InvoiceAnalysis> {
    if (!this.isConfigured()) {
      throw new AppError('AI service not configured', 503);
    }

    try {
      const prompt = `
        Extract structured information from this invoice:

        Invoice Content:
        ${invoiceContent}

        Please extract:
        1. Total amount
        2. Line items with description, quantity, unit price, and total
        3. Due date
        4. Customer information (name, address, email)
        5. Confidence level of extraction

        Respond in JSON format:
        {
          "totalAmount": 1234.56,
          "lineItems": [
            {
              "description": "...",
              "quantity": 1,
              "unitPrice": 100.00,
              "total": 100.00
            }
          ],
          "dueDate": "2024-01-15",
          "customerInfo": {
            "name": "...",
            "address": "...",
            "email": "..."
          },
          "confidence": 0.95
        }
      `;

      const result = await this.model.generateContent(prompt);
      const response = await result.response;
      const text = response.text();

      const jsonMatch = text.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('Invalid AI response format');
      }

      const analysis = JSON.parse(jsonMatch[0]);
      return analysis;

    } catch (error) {
      console.error('Invoice analysis error:', error);
      throw new AppError('Failed to analyze invoice', 500);
    }
  }

  async generateInsights(data: Record<string, any>, domain: string): Promise<string[]> {
    if (!this.isConfigured()) {
      throw new AppError('AI service not configured', 503);
    }

    try {
      const prompt = `
        Analyze the following ${domain} data and generate actionable business insights:

        Data:
        ${JSON.stringify(data, null, 2)}

        Please provide 3-5 specific, actionable insights that could help improve business operations.
        Focus on trends, anomalies, opportunities, and recommendations.

        Respond as a JSON array of strings:
        ["Insight 1...", "Insight 2...", "Insight 3..."]
      `;

      const result = await this.model.generateContent(prompt);
      const response = await result.response;
      const text = response.text();

      const jsonMatch = text.match(/\[[\s\S]*\]/);
      if (!jsonMatch) {
        throw new Error('Invalid AI response format');
      }

      const insights = JSON.parse(jsonMatch[0]);
      return insights;

    } catch (error) {
      console.error('Insights generation error:', error);
      throw new AppError('Failed to generate insights', 500);
    }
  }

  async processNaturalLanguageQuery(query: string, context?: Record<string, any>): Promise<string> {
    if (!this.isConfigured()) {
      throw new AppError('AI service not configured', 503);
    }

    try {
      const prompt = `
        You are an AI assistant for the AR-SCIA business intelligence platform.
        The platform handles accounts receivable automation and supply chain intelligence.

        User Query: ${query}

        ${context ? `Available Context: ${JSON.stringify(context, null, 2)}` : ''}

        Please provide a helpful, accurate response based on the query and available context.
        If you need more specific data to answer the question, suggest what information would be helpful.
      `;

      const result = await this.model.generateContent(prompt);
      const response = await result.response;
      return response.text();

    } catch (error) {
      console.error('Natural language query error:', error);
      throw new AppError('Failed to process query', 500);
    }
  }

  async validateDataQuality(data: Record<string, any>, schema: Record<string, any>): Promise<{
    isValid: boolean;
    issues: string[];
    suggestions: string[];
    confidence: number;
  }> {
    if (!this.isConfigured()) {
      throw new AppError('AI service not configured', 503);
    }

    try {
      const prompt = `
        Validate the following data against the expected schema and identify quality issues:

        Data:
        ${JSON.stringify(data, null, 2)}

        Expected Schema:
        ${JSON.stringify(schema, null, 2)}

        Please check for:
        1. Missing required fields
        2. Invalid data types
        3. Data format issues
        4. Logical inconsistencies
        5. Potential data quality problems

        Respond in JSON format:
        {
          "isValid": true|false,
          "issues": ["Issue 1...", "Issue 2..."],
          "suggestions": ["Suggestion 1...", "Suggestion 2..."],
          "confidence": 0.95
        }
      `;

      const result = await this.model.generateContent(prompt);
      const response = await result.response;
      const text = response.text();

      const jsonMatch = text.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('Invalid AI response format');
      }

      const validation = JSON.parse(jsonMatch[0]);
      return validation;

    } catch (error) {
      console.error('Data validation error:', error);
      throw new AppError('Failed to validate data quality', 500);
    }
  }
}
