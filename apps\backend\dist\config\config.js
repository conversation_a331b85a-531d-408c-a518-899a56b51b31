"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.config = void 0;
const zod_1 = require("zod");
const dotenv_1 = __importDefault(require("dotenv"));
dotenv_1.default.config();
const configSchema = zod_1.z.object({
    nodeEnv: zod_1.z.enum(['development', 'production', 'test']).default('development'),
    port: zod_1.z.coerce.number().default(3001),
    databaseUrl: zod_1.z.string().min(1),
    redisUrl: zod_1.z.string().min(1),
    jwtSecret: zod_1.z.string().min(32),
    jwtExpiresIn: zod_1.z.string().default('24h'),
    corsOrigin: zod_1.z.string().default('http://localhost:3000'),
    geminiApiKey: zod_1.z.string().min(1),
    uploadMaxSize: zod_1.z.coerce.number().default(10 * 1024 * 1024),
    uploadPath: zod_1.z.string().default('./uploads'),
    emailFrom: zod_1.z.string().email().optional().or(zod_1.z.literal('')),
    emailApiKey: zod_1.z.string().optional(),
    xeroClientId: zod_1.z.string().optional(),
    xeroClientSecret: zod_1.z.string().optional(),
    fishbowlApiUrl: zod_1.z.string().optional(),
    fishbowlApiKey: zod_1.z.string().optional(),
});
const env = {
    nodeEnv: process.env.NODE_ENV,
    port: process.env.PORT,
    databaseUrl: process.env.DATABASE_URL,
    redisUrl: process.env.REDIS_URL,
    jwtSecret: process.env.JWT_SECRET,
    jwtExpiresIn: process.env.JWT_EXPIRES_IN,
    corsOrigin: process.env.CORS_ORIGIN,
    geminiApiKey: process.env.GEMINI_API_KEY,
    uploadMaxSize: process.env.UPLOAD_MAX_SIZE,
    uploadPath: process.env.UPLOAD_PATH,
    emailFrom: process.env.EMAIL_FROM,
    emailApiKey: process.env.EMAIL_API_KEY,
    xeroClientId: process.env.XERO_CLIENT_ID,
    xeroClientSecret: process.env.XERO_CLIENT_SECRET,
    fishbowlApiUrl: process.env.FISHBOWL_API_URL,
    fishbowlApiKey: process.env.FISHBOWL_API_KEY,
};
exports.config = configSchema.parse(env);
if (exports.config.nodeEnv === 'production') {
    const requiredVars = [
        'DATABASE_URL',
        'REDIS_URL',
        'JWT_SECRET',
        'GEMINI_API_KEY'
    ];
    const missingVars = requiredVars.filter(varName => !process.env[varName]);
    if (missingVars.length > 0) {
        console.error('❌ Missing required environment variables:', missingVars);
        process.exit(1);
    }
}
console.log('✅ Configuration loaded successfully');
console.log(`📊 Environment: ${exports.config.nodeEnv}`);
console.log(`🔌 Port: ${exports.config.port}`);
console.log(`🔗 CORS Origin: ${exports.config.corsOrigin}`);
//# sourceMappingURL=config.js.map