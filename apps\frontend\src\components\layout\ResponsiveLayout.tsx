import React, { useState, useEffect } from 'react';
import { cn } from '@/lib/utils';
import { MobileNavigation } from '@/components/mobile/MobileNavigation';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Bell, Search, User, Settings, Menu, 
  BarChart3, Home, DollarSign, Package, 
  Users, AlertTriangle, LogOut
} from 'lucide-react';
import { useAuth } from '@/hooks/useAuth';

interface ResponsiveLayoutProps {
  children: React.ReactNode;
  currentPath: string;
  onNavigate: (path: string) => void;
}

interface NavigationItem {
  id: string;
  label: string;
  icon: React.ReactNode;
  href: string;
  badge?: string;
}

export const ResponsiveLayout: React.FC<ResponsiveLayoutProps> = ({ 
  children, 
  currentPath, 
  onNavigate 
}) => {
  const [isMobile, setIsMobile] = useState(false);
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const { user, logout } = useAuth();

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
      if (window.innerWidth < 1024) {
        setSidebarCollapsed(true);
      }
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  const navigationItems: NavigationItem[] = [
    {
      id: 'dashboard',
      label: 'Dashboard',
      icon: <Home className="w-5 h-5" />,
      href: '/dashboard'
    },
    {
      id: 'ar',
      label: 'Accounts Receivable',
      icon: <DollarSign className="w-5 h-5" />,
      href: '/ar',
      badge: '12'
    },
    {
      id: 'supply-chain',
      label: 'Supply Chain',
      icon: <Package className="w-5 h-5" />,
      href: '/supply-chain',
      badge: '3'
    },
    {
      id: 'customers',
      label: 'Customers',
      icon: <Users className="w-5 h-5" />,
      href: '/customers'
    },
    {
      id: 'analytics',
      label: 'Analytics',
      icon: <BarChart3 className="w-5 h-5" />,
      href: '/analytics'
    },
    {
      id: 'alerts',
      label: 'Alerts',
      icon: <AlertTriangle className="w-5 h-5" />,
      href: '/alerts',
      badge: '7'
    }
  ];

  const isActive = (href: string) => {
    return currentPath === href || currentPath.startsWith(href + '/');
  };

  // Mobile Layout
  if (isMobile) {
    return (
      <div className="min-h-screen bg-background">
        {/* Mobile Header */}
        <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
          <div className="container flex h-14 items-center justify-between px-4">
            <div className="flex items-center gap-3">
              <MobileNavigation currentPath={currentPath} onNavigate={onNavigate} />
              <div className="flex items-center gap-2">
                <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
                  <BarChart3 className="w-5 h-5 text-primary-foreground" />
                </div>
                <span className="font-semibold">AR-SCIA</span>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Button variant="ghost" size="icon" className="h-8 w-8">
                <Search className="h-4 w-4" />
              </Button>
              <Button variant="ghost" size="icon" className="h-8 w-8 relative">
                <Bell className="h-4 w-4" />
                <Badge className="absolute -top-1 -right-1 h-4 w-4 p-0 text-xs">3</Badge>
              </Button>
              <Button variant="ghost" size="icon" className="h-8 w-8">
                <User className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </header>

        {/* Mobile Content */}
        <main className="container px-4 py-4">
          {children}
        </main>

        {/* Mobile Bottom Navigation */}
        <nav className="fixed bottom-0 left-0 right-0 z-50 bg-background border-t">
          <div className="grid grid-cols-5 gap-1 p-2">
            {navigationItems.slice(0, 5).map((item) => (
              <Button
                key={item.id}
                variant={isActive(item.href) ? 'secondary' : 'ghost'}
                size="sm"
                className="flex flex-col gap-1 h-12 relative"
                onClick={() => onNavigate(item.href)}
              >
                {item.icon}
                <span className="text-xs truncate">{item.label.split(' ')[0]}</span>
                {item.badge && (
                  <Badge className="absolute -top-1 -right-1 h-4 w-4 p-0 text-xs">
                    {item.badge}
                  </Badge>
                )}
              </Button>
            ))}
          </div>
        </nav>
      </div>
    );
  }

  // Desktop Layout
  return (
    <div className="min-h-screen bg-background">
      {/* Desktop Sidebar */}
      <aside className={cn(
        "fixed left-0 top-0 z-40 h-screen border-r bg-background transition-all duration-300",
        sidebarCollapsed ? "w-16" : "w-64"
      )}>
        <div className="flex h-full flex-col">
          {/* Sidebar Header */}
          <div className="flex h-14 items-center border-b px-4">
            <div className="flex items-center gap-2">
              <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
                <BarChart3 className="w-5 h-5 text-primary-foreground" />
              </div>
              {!sidebarCollapsed && (
                <div>
                  <div className="font-semibold">AR-SCIA</div>
                  <div className="text-xs text-muted-foreground">Business Intelligence</div>
                </div>
              )}
            </div>
            <Button
              variant="ghost"
              size="icon"
              className="ml-auto h-8 w-8"
              onClick={() => setSidebarCollapsed(!sidebarCollapsed)}
            >
              <Menu className="h-4 w-4" />
            </Button>
          </div>

          {/* Navigation */}
          <nav className="flex-1 space-y-1 p-2">
            {navigationItems.map((item) => (
              <Button
                key={item.id}
                variant={isActive(item.href) ? 'secondary' : 'ghost'}
                className={cn(
                  "w-full justify-start gap-3 relative",
                  sidebarCollapsed ? "px-2" : "px-3"
                )}
                onClick={() => onNavigate(item.href)}
                title={sidebarCollapsed ? item.label : undefined}
              >
                {item.icon}
                {!sidebarCollapsed && (
                  <>
                    <span className="flex-1 text-left">{item.label}</span>
                    {item.badge && (
                      <Badge variant="destructive" className="ml-auto">
                        {item.badge}
                      </Badge>
                    )}
                  </>
                )}
                {sidebarCollapsed && item.badge && (
                  <Badge className="absolute -top-1 -right-1 h-4 w-4 p-0 text-xs">
                    {item.badge}
                  </Badge>
                )}
              </Button>
            ))}
          </nav>

          {/* Sidebar Footer */}
          <div className="border-t p-2 space-y-1">
            <Button
              variant="ghost"
              className={cn(
                "w-full justify-start gap-3",
                sidebarCollapsed ? "px-2" : "px-3"
              )}
              onClick={() => onNavigate('/settings')}
              title={sidebarCollapsed ? 'Settings' : undefined}
            >
              <Settings className="w-5 h-5" />
              {!sidebarCollapsed && <span className="flex-1 text-left">Settings</span>}
            </Button>
            
            <Button
              variant="ghost"
              className={cn(
                "w-full justify-start gap-3 text-red-600 hover:text-red-700 hover:bg-red-50",
                sidebarCollapsed ? "px-2" : "px-3"
              )}
              onClick={logout}
              title={sidebarCollapsed ? 'Sign Out' : undefined}
            >
              <LogOut className="w-5 h-5" />
              {!sidebarCollapsed && <span className="flex-1 text-left">Sign Out</span>}
            </Button>
          </div>
        </div>
      </aside>

      {/* Desktop Header */}
      <header className={cn(
        "sticky top-0 z-30 border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 transition-all duration-300",
        sidebarCollapsed ? "ml-16" : "ml-64"
      )}>
        <div className="flex h-14 items-center justify-between px-6">
          <div className="flex items-center gap-4">
            <h1 className="text-lg font-semibold">
              {navigationItems.find(item => isActive(item.href))?.label || 'Dashboard'}
            </h1>
          </div>
          
          <div className="flex items-center gap-4">
            <Button variant="ghost" size="icon">
              <Search className="h-4 w-4" />
            </Button>
            
            <Button variant="ghost" size="icon" className="relative">
              <Bell className="h-4 w-4" />
              <Badge className="absolute -top-1 -right-1 h-4 w-4 p-0 text-xs">3</Badge>
            </Button>
            
            <div className="flex items-center gap-2 pl-4 border-l">
              <div className="w-8 h-8 bg-primary rounded-full flex items-center justify-center">
                <User className="w-4 h-4 text-primary-foreground" />
              </div>
              <div className="text-sm">
                <div className="font-medium">{user?.name || 'User'}</div>
                <div className="text-muted-foreground">{user?.role || 'Admin'}</div>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Desktop Content */}
      <main className={cn(
        "transition-all duration-300 p-6",
        sidebarCollapsed ? "ml-16" : "ml-64"
      )}>
        {children}
      </main>
    </div>
  );
};
