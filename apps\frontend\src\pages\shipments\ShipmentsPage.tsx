import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import {
  Package,
  Plus,
  Search,
  Filter,
  Download,
  Truck,
  MapPin,
  Calendar,
  Building2,
  AlertTriangle,
  CheckCircle,
  Clock,
  Eye,
  Route,
  Plane,
  Ship,
  Navigation,
  BarChart3
} from 'lucide-react';

interface Shipment {
  id: string;
  trackingNumber: string;
  customerName: string;
  customerId: string;
  origin: string;
  destination: string;
  status: 'pending' | 'in-transit' | 'delivered' | 'delayed' | 'cancelled';
  carrier: string;
  service: string;
  shipDate: string;
  estimatedDelivery: string;
  actualDelivery?: string;
  weight: number;
  dimensions: string;
  value: number;
  invoiceNumber?: string;
  items: Array<{
    name: string;
    quantity: number;
    sku: string;
  }>;
}

interface ShipmentMetrics {
  total: number;
  inTransit: number;
  delivered: number;
  delayed: number;
  onTimeRate: number;
  avgDeliveryDays: number;
}

// Mock data - in real app this would come from API
const mockShipments: Shipment[] = [
  {
    id: '1',
    trackingNumber: 'TRK-2024-001',
    customerName: 'Acme Corporation',
    customerId: '1',
    origin: 'New York, NY',
    destination: 'Los Angeles, CA',
    status: 'in-transit',
    carrier: 'FedEx',
    service: 'Express',
    shipDate: '2024-06-25',
    estimatedDelivery: '2024-06-28',
    weight: 15.5,
    dimensions: '12x8x6 in',
    value: 2500,
    invoiceNumber: 'INV-2024-001',
    items: [
      { name: 'Software License Key', quantity: 1, sku: 'SLK-001' },
      { name: 'Hardware Component', quantity: 2, sku: 'HWC-002' }
    ]
  },
  {
    id: '2',
    trackingNumber: 'TRK-2024-002',
    customerName: 'TechStart Inc',
    customerId: '2',
    origin: 'Chicago, IL',
    destination: 'Miami, FL',
    status: 'delivered',
    carrier: 'UPS',
    service: 'Ground',
    shipDate: '2024-06-20',
    estimatedDelivery: '2024-06-24',
    actualDelivery: '2024-06-23',
    weight: 8.2,
    dimensions: '10x6x4 in',
    value: 1200,
    invoiceNumber: 'INV-2024-002',
    items: [
      { name: 'Consulting Materials', quantity: 1, sku: 'CM-001' }
    ]
  },
  {
    id: '3',
    trackingNumber: 'TRK-2024-003',
    customerName: 'Global Solutions LLC',
    customerId: '3',
    origin: 'Seattle, WA',
    destination: 'Boston, MA',
    status: 'delayed',
    carrier: 'DHL',
    service: 'Standard',
    shipDate: '2024-06-22',
    estimatedDelivery: '2024-06-26',
    weight: 22.1,
    dimensions: '16x12x8 in',
    value: 3200,
    items: [
      { name: 'Server Equipment', quantity: 1, sku: 'SE-001' },
      { name: 'Network Cables', quantity: 5, sku: 'NC-002' }
    ]
  },
  {
    id: '4',
    trackingNumber: 'TRK-2024-004',
    customerName: 'Innovation Labs',
    customerId: '4',
    origin: 'Austin, TX',
    destination: 'Portland, OR',
    status: 'pending',
    carrier: 'USPS',
    service: 'Priority',
    shipDate: '2024-06-28',
    estimatedDelivery: '2024-07-02',
    weight: 5.8,
    dimensions: '8x6x4 in',
    value: 800,
    items: [
      { name: 'Documentation Package', quantity: 1, sku: 'DP-001' }
    ]
  },
  {
    id: '5',
    trackingNumber: 'TRK-2024-005',
    customerName: 'Digital Dynamics',
    customerId: '5',
    origin: 'Denver, CO',
    destination: 'Atlanta, GA',
    status: 'in-transit',
    carrier: 'FedEx',
    service: 'Overnight',
    shipDate: '2024-06-27',
    estimatedDelivery: '2024-06-28',
    weight: 12.3,
    dimensions: '14x10x6 in',
    value: 1800,
    items: [
      { name: 'Custom Software', quantity: 1, sku: 'CS-001' },
      { name: 'Installation Guide', quantity: 1, sku: 'IG-001' }
    ]
  }
];

const mockMetrics: ShipmentMetrics = {
  total: 45,
  inTransit: 12,
  delivered: 28,
  delayed: 3,
  onTimeRate: 94.2,
  avgDeliveryDays: 3.2
};

export const ShipmentsPage: React.FC = () => {
  const [shipments, setShipments] = useState<Shipment[]>([]);
  const [metrics, setMetrics] = useState<ShipmentMetrics | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [filteredShipments, setFilteredShipments] = useState<Shipment[]>([]);

  useEffect(() => {
    // Simulate API calls
    setShipments(mockShipments);
    setMetrics(mockMetrics);
    setFilteredShipments(mockShipments);
  }, []);

  useEffect(() => {
    let filtered = shipments.filter(shipment =>
      shipment.trackingNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
      shipment.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      shipment.destination.toLowerCase().includes(searchTerm.toLowerCase()) ||
      shipment.carrier.toLowerCase().includes(searchTerm.toLowerCase())
    );

    if (statusFilter !== 'all') {
      filtered = filtered.filter(shipment => shipment.status === statusFilter);
    }

    setFilteredShipments(filtered);
  }, [searchTerm, statusFilter, shipments]);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getStatusColor = (status: Shipment['status']) => {
    switch (status) {
      case 'delivered':
        return 'bg-green-100 text-green-800';
      case 'in-transit':
        return 'bg-blue-100 text-blue-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'delayed':
        return 'bg-red-100 text-red-800';
      case 'cancelled':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: Shipment['status']) => {
    switch (status) {
      case 'delivered':
        return <CheckCircle className="h-4 w-4" />;
      case 'in-transit':
        return <Truck className="h-4 w-4" />;
      case 'pending':
        return <Clock className="h-4 w-4" />;
      case 'delayed':
        return <AlertTriangle className="h-4 w-4" />;
      case 'cancelled':
        return <Package className="h-4 w-4" />;
      default:
        return <Package className="h-4 w-4" />;
    }
  };

  const getCarrierIcon = (carrier: string) => {
    switch (carrier.toLowerCase()) {
      case 'fedex':
        return <Plane className="h-4 w-4" />;
      case 'ups':
        return <Truck className="h-4 w-4" />;
      case 'dhl':
        return <Ship className="h-4 w-4" />;
      case 'usps':
        return <Package className="h-4 w-4" />;
      default:
        return <Truck className="h-4 w-4" />;
    }
  };

  if (!metrics) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <Package className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <p className="text-muted-foreground">Loading shipments...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Supply Chain Intelligence</h2>
          <p className="text-muted-foreground">
            Track shipments and manage logistics operations
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline">
            <Download className="mr-2 h-4 w-4" />
            Export
          </Button>
          <Button>
            <Plus className="mr-2 h-4 w-4" />
            New Shipment
          </Button>
        </div>
      </div>

      {/* Shipment Metrics */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Shipments</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.total}</div>
            <p className="text-xs text-muted-foreground">
              Active shipments
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">In Transit</CardTitle>
            <Truck className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.inTransit}</div>
            <p className="text-xs text-muted-foreground">
              Currently shipping
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Delivered</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.delivered}</div>
            <p className="text-xs text-muted-foreground">
              Successfully delivered
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Delayed</CardTitle>
            <AlertTriangle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.delayed}</div>
            <p className="text-xs text-muted-foreground">
              Require attention
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">On-Time Rate</CardTitle>
            <BarChart3 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.onTimeRate}%</div>
            <p className="text-xs text-muted-foreground">
              Delivery performance
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Delivery</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.avgDeliveryDays}</div>
            <p className="text-xs text-muted-foreground">
              Days to deliver
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Shipment Management</CardTitle>
          <CardDescription>
            Search and filter shipments by tracking number, customer, or destination
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-4 mb-6">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search by tracking number, customer, destination, or carrier..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-8"
                />
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Filter className="h-4 w-4 text-muted-foreground" />
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="px-3 py-2 border border-input bg-background rounded-md text-sm"
              >
                <option value="all">All Status</option>
                <option value="pending">Pending</option>
                <option value="in-transit">In Transit</option>
                <option value="delivered">Delivered</option>
                <option value="delayed">Delayed</option>
                <option value="cancelled">Cancelled</option>
              </select>
            </div>
          </div>

          {/* Shipments List */}
          {filteredShipments.length === 0 ? (
            <div className="text-center py-12">
              <Package className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-medium mb-2">No shipments found</h3>
              <p className="text-muted-foreground mb-4">
                {searchTerm || statusFilter !== 'all'
                  ? 'Try adjusting your search or filter criteria'
                  : 'Create your first shipment to get started'
                }
              </p>
              {(!searchTerm && statusFilter === 'all') && (
                <Button>
                  <Plus className="mr-2 h-4 w-4" />
                  Create Shipment
                </Button>
              )}
            </div>
          ) : (
            <div className="space-y-4">
              {filteredShipments.map((shipment) => (
                <div key={shipment.id} className="border rounded-lg p-6 hover:shadow-md transition-shadow">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-center space-x-3">
                      <div className="flex items-center justify-center w-10 h-10 bg-primary/10 rounded-lg">
                        {getCarrierIcon(shipment.carrier)}
                      </div>
                      <div>
                        <h3 className="font-semibold text-lg">{shipment.trackingNumber}</h3>
                        <p className="text-sm text-muted-foreground">{shipment.carrier} {shipment.service}</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Badge className={getStatusColor(shipment.status)}>
                        {getStatusIcon(shipment.status)}
                        <span className="ml-1 capitalize">{shipment.status.replace('-', ' ')}</span>
                      </Badge>
                      <Button variant="outline" size="sm">
                        <Eye className="mr-2 h-4 w-4" />
                        View Details
                      </Button>
                    </div>
                  </div>

                  <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4 mb-4">
                    <div className="flex items-center space-x-2">
                      <Building2 className="h-4 w-4 text-muted-foreground" />
                      <div>
                        <p className="text-sm font-medium">{shipment.customerName}</p>
                        <p className="text-xs text-muted-foreground">Customer</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Route className="h-4 w-4 text-muted-foreground" />
                      <div>
                        <p className="text-sm font-medium">{shipment.origin} → {shipment.destination}</p>
                        <p className="text-xs text-muted-foreground">Route</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Calendar className="h-4 w-4 text-muted-foreground" />
                      <div>
                        <p className="text-sm font-medium">
                          {shipment.actualDelivery ? formatDate(shipment.actualDelivery) : formatDate(shipment.estimatedDelivery)}
                        </p>
                        <p className="text-xs text-muted-foreground">
                          {shipment.actualDelivery ? 'Delivered' : 'Est. Delivery'}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Package className="h-4 w-4 text-muted-foreground" />
                      <div>
                        <p className="text-sm font-medium">{formatCurrency(shipment.value)}</p>
                        <p className="text-xs text-muted-foreground">{shipment.weight} lbs</p>
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center justify-between pt-4 border-t">
                    <div className="flex items-center space-x-4">
                      <span className="text-sm text-muted-foreground">
                        Items: {shipment.items.reduce((sum, item) => sum + item.quantity, 0)}
                      </span>
                      <span className="text-sm text-muted-foreground">
                        Dimensions: {shipment.dimensions}
                      </span>
                      {shipment.invoiceNumber && (
                        <Link
                          to={`/invoices/${shipment.invoiceNumber}`}
                          className="text-sm text-primary hover:underline"
                        >
                          Invoice: {shipment.invoiceNumber}
                        </Link>
                      )}
                    </div>
                    <div className="flex items-center space-x-2">
                      <Button variant="outline" size="sm">
                        <Navigation className="mr-2 h-4 w-4" />
                        Track
                      </Button>
                      <Link to={`/customers/${shipment.customerId}`}>
                        <Button variant="outline" size="sm">
                          <Building2 className="mr-2 h-4 w-4" />
                          Customer
                        </Button>
                      </Link>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};