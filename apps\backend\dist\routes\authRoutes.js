"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.authRoutes = void 0;
const express_1 = require("express");
const auth_1 = require("@/middleware/auth");
const authController_1 = require("@/controllers/authController");
const router = (0, express_1.Router)();
exports.authRoutes = router;
router.post('/login', authController_1.login);
router.post('/register', authController_1.register);
router.post('/logout', auth_1.authenticateToken, authController_1.logout);
router.post('/refresh', authController_1.refreshToken);
router.get('/me', auth_1.authenticateToken, authController_1.getCurrentUser);
//# sourceMappingURL=authRoutes.js.map