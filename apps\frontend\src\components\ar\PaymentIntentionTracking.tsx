import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { 
  CalendarIcon, Clock, CheckCircle, XCircle, AlertTriangle, 
  Plus, Edit, Trash2, Phone, Mail, FileText, TrendingUp,
  Users, DollarSign, Target, Activity
} from 'lucide-react';
import { format } from 'date-fns';

interface PaymentCommitment {
  id: string;
  customerId: string;
  customerName: string;
  invoiceId: string;
  invoiceNumber: string;
  originalAmount: number;
  commitmentAmount: number;
  commitmentDate: Date;
  paymentMethod: string;
  status: 'pending' | 'confirmed' | 'partial' | 'fulfilled' | 'broken' | 'renegotiated';
  confidence: number;
  source: 'email' | 'phone' | 'portal' | 'meeting';
  createdDate: Date;
  lastUpdated: Date;
  notes: string;
  followUpActions: FollowUpAction[];
  escalationLevel: 'none' | 'manager' | 'legal' | 'collections';
}

interface FollowUpAction {
  id: string;
  type: 'call' | 'email' | 'meeting' | 'legal_notice' | 'payment_plan';
  scheduledDate: Date;
  description: string;
  assignedTo: string;
  status: 'pending' | 'completed' | 'cancelled';
  result?: string;
}

interface PaymentIntentionMetrics {
  totalCommitments: number;
  totalCommittedAmount: number;
  fulfillmentRate: number;
  averageCommitmentTime: number;
  brokenCommitments: number;
  renegotiatedCommitments: number;
}

export const PaymentIntentionTracking: React.FC = () => {
  const [commitments, setCommitments] = useState<PaymentCommitment[]>([]);
  const [metrics, setMetrics] = useState<PaymentIntentionMetrics | null>(null);
  const [selectedCommitment, setSelectedCommitment] = useState<PaymentCommitment | null>(null);
  const [loading, setLoading] = useState(true);
  const [showNewCommitment, setShowNewCommitment] = useState(false);
  const [filterStatus, setFilterStatus] = useState('all');
  const [filterEscalation, setFilterEscalation] = useState('all');

  // New commitment form
  const [newCommitment, setNewCommitment] = useState({
    customerId: '',
    customerName: '',
    invoiceId: '',
    invoiceNumber: '',
    originalAmount: 0,
    commitmentAmount: 0,
    commitmentDate: new Date(),
    paymentMethod: '',
    confidence: 80,
    source: 'email' as const,
    notes: ''
  });

  useEffect(() => {
    loadCommitments();
    loadMetrics();
  }, []);

  const loadCommitments = async () => {
    setLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const mockCommitments: PaymentCommitment[] = [
        {
          id: '1',
          customerId: 'cust-1',
          customerName: 'Acme Corporation',
          invoiceId: 'inv-1',
          invoiceNumber: 'INV-2024-001',
          originalAmount: 25000,
          commitmentAmount: 25000,
          commitmentDate: new Date('2024-03-15'),
          paymentMethod: 'Wire Transfer',
          status: 'pending',
          confidence: 85,
          source: 'email',
          createdDate: new Date('2024-02-28'),
          lastUpdated: new Date('2024-02-28'),
          notes: 'Customer confirmed payment by March 15th via email response',
          followUpActions: [
            {
              id: 'action-1',
              type: 'call',
              scheduledDate: new Date('2024-03-10'),
              description: 'Confirmation call 5 days before commitment date',
              assignedTo: 'John Smith',
              status: 'pending'
            }
          ],
          escalationLevel: 'none'
        },
        {
          id: '2',
          customerId: 'cust-2',
          customerName: 'Global Industries',
          invoiceId: 'inv-2',
          invoiceNumber: 'INV-2024-002',
          originalAmount: 18500,
          commitmentAmount: 10000,
          commitmentDate: new Date('2024-03-01'),
          paymentMethod: 'Credit Card',
          status: 'partial',
          confidence: 70,
          source: 'phone',
          createdDate: new Date('2024-02-20'),
          lastUpdated: new Date('2024-03-01'),
          notes: 'Partial payment received, remaining balance renegotiated',
          followUpActions: [],
          escalationLevel: 'manager'
        },
        {
          id: '3',
          customerId: 'cust-3',
          customerName: 'Tech Solutions Ltd',
          invoiceId: 'inv-3',
          invoiceNumber: 'INV-2024-003',
          originalAmount: 12000,
          commitmentAmount: 12000,
          commitmentDate: new Date('2024-02-25'),
          paymentMethod: 'ACH',
          status: 'fulfilled',
          confidence: 95,
          source: 'portal',
          createdDate: new Date('2024-02-15'),
          lastUpdated: new Date('2024-02-25'),
          notes: 'Payment received on time as committed',
          followUpActions: [],
          escalationLevel: 'none'
        }
      ];

      setCommitments(mockCommitments);
    } catch (error) {
      console.error('Failed to load commitments:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadMetrics = async () => {
    try {
      // Simulate API call
      const mockMetrics: PaymentIntentionMetrics = {
        totalCommitments: 45,
        totalCommittedAmount: 1250000,
        fulfillmentRate: 78.5,
        averageCommitmentTime: 12.5,
        brokenCommitments: 8,
        renegotiatedCommitments: 5
      };

      setMetrics(mockMetrics);
    } catch (error) {
      console.error('Failed to load metrics:', error);
    }
  };

  const createCommitment = async () => {
    try {
      const commitment: PaymentCommitment = {
        id: `comm_${Date.now()}`,
        ...newCommitment,
        status: 'pending',
        createdDate: new Date(),
        lastUpdated: new Date(),
        followUpActions: [],
        escalationLevel: 'none'
      };

      setCommitments(prev => [commitment, ...prev]);
      setShowNewCommitment(false);
      
      // Reset form
      setNewCommitment({
        customerId: '',
        customerName: '',
        invoiceId: '',
        invoiceNumber: '',
        originalAmount: 0,
        commitmentAmount: 0,
        commitmentDate: new Date(),
        paymentMethod: '',
        confidence: 80,
        source: 'email',
        notes: ''
      });
    } catch (error) {
      console.error('Failed to create commitment:', error);
    }
  };

  const updateCommitmentStatus = async (commitmentId: string, status: PaymentCommitment['status']) => {
    try {
      setCommitments(prev => prev.map(c => 
        c.id === commitmentId 
          ? { ...c, status, lastUpdated: new Date() }
          : c
      ));
    } catch (error) {
      console.error('Failed to update commitment status:', error);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  const getStatusBadge = (status: PaymentCommitment['status']) => {
    const variants = {
      pending: 'default',
      confirmed: 'secondary',
      partial: 'secondary',
      fulfilled: 'success',
      broken: 'destructive',
      renegotiated: 'secondary'
    } as const;

    const icons = {
      pending: Clock,
      confirmed: CheckCircle,
      partial: Activity,
      fulfilled: CheckCircle,
      broken: XCircle,
      renegotiated: Edit
    };

    const Icon = icons[status];

    return (
      <Badge variant={variants[status]}>
        <Icon className="w-3 h-3 mr-1" />
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    );
  };

  const getEscalationBadge = (level: PaymentCommitment['escalationLevel']) => {
    if (level === 'none') return null;

    const variants = {
      manager: 'secondary',
      legal: 'destructive',
      collections: 'destructive'
    } as const;

    return (
      <Badge variant={variants[level]} className="ml-2">
        <AlertTriangle className="w-3 h-3 mr-1" />
        {level.charAt(0).toUpperCase() + level.slice(1)}
      </Badge>
    );
  };

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 80) return 'text-green-600';
    if (confidence >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  const filteredCommitments = commitments.filter(commitment => {
    if (filterStatus !== 'all' && commitment.status !== filterStatus) return false;
    if (filterEscalation !== 'all' && commitment.escalationLevel !== filterEscalation) return false;
    return true;
  });

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="mt-2 text-muted-foreground">Loading payment intentions...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Payment Intention Tracking</h1>
          <p className="text-muted-foreground">Monitor customer payment commitments and follow-up activities</p>
        </div>
        <Button onClick={() => setShowNewCommitment(true)}>
          <Plus className="w-4 h-4 mr-2" />
          New Commitment
        </Button>
      </div>

      {/* Metrics */}
      {metrics && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Commitments</CardTitle>
              <Target className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{metrics.totalCommitments}</div>
              <p className="text-xs text-muted-foreground">
                {formatCurrency(metrics.totalCommittedAmount)} committed
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Fulfillment Rate</CardTitle>
              <TrendingUp className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">{metrics.fulfillmentRate}%</div>
              <p className="text-xs text-muted-foreground">
                Target: 85%
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Avg Commitment Time</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{metrics.averageCommitmentTime} days</div>
              <p className="text-xs text-muted-foreground">
                From commitment to payment
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Broken Commitments</CardTitle>
              <XCircle className="h-4 w-4 text-destructive" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-destructive">{metrics.brokenCommitments}</div>
              <p className="text-xs text-muted-foreground">
                {metrics.renegotiatedCommitments} renegotiated
              </p>
            </CardContent>
          </Card>
        </div>
      )}

      <Tabs defaultValue="commitments" className="w-full">
        <TabsList>
          <TabsTrigger value="commitments">Active Commitments</TabsTrigger>
          <TabsTrigger value="followups">Follow-up Actions</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="commitments" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Payment Commitments</CardTitle>
              <CardDescription>Track and manage customer payment commitments</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex items-center gap-4 mb-4">
                <div className="flex items-center gap-2">
                  <Label>Status:</Label>
                  <Select value={filterStatus} onValueChange={setFilterStatus}>
                    <SelectTrigger className="w-32">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All</SelectItem>
                      <SelectItem value="pending">Pending</SelectItem>
                      <SelectItem value="confirmed">Confirmed</SelectItem>
                      <SelectItem value="partial">Partial</SelectItem>
                      <SelectItem value="fulfilled">Fulfilled</SelectItem>
                      <SelectItem value="broken">Broken</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="flex items-center gap-2">
                  <Label>Escalation:</Label>
                  <Select value={filterEscalation} onValueChange={setFilterEscalation}>
                    <SelectTrigger className="w-32">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All</SelectItem>
                      <SelectItem value="none">None</SelectItem>
                      <SelectItem value="manager">Manager</SelectItem>
                      <SelectItem value="legal">Legal</SelectItem>
                      <SelectItem value="collections">Collections</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b">
                      <th className="text-left p-2">Customer</th>
                      <th className="text-left p-2">Invoice</th>
                      <th className="text-right p-2">Amount</th>
                      <th className="text-center p-2">Commitment Date</th>
                      <th className="text-center p-2">Confidence</th>
                      <th className="text-center p-2">Status</th>
                      <th className="text-center p-2">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {filteredCommitments.map((commitment) => (
                      <tr key={commitment.id} className="border-b hover:bg-muted/50">
                        <td className="p-2">
                          <div>
                            <div className="font-medium">{commitment.customerName}</div>
                            <div className="text-sm text-muted-foreground">
                              Source: {commitment.source}
                            </div>
                          </div>
                        </td>
                        <td className="p-2">
                          <div>
                            <div className="font-medium">{commitment.invoiceNumber}</div>
                            <div className="text-sm text-muted-foreground">
                              Original: {formatCurrency(commitment.originalAmount)}
                            </div>
                          </div>
                        </td>
                        <td className="text-right p-2">
                          <div className="font-medium">{formatCurrency(commitment.commitmentAmount)}</div>
                          <div className="text-sm text-muted-foreground">
                            {commitment.paymentMethod}
                          </div>
                        </td>
                        <td className="text-center p-2">
                          <div className="font-medium">
                            {format(commitment.commitmentDate, 'MMM dd, yyyy')}
                          </div>
                          <div className="text-sm text-muted-foreground">
                            {Math.ceil((commitment.commitmentDate.getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24))} days
                          </div>
                        </td>
                        <td className="text-center p-2">
                          <div className={`font-medium ${getConfidenceColor(commitment.confidence)}`}>
                            {commitment.confidence}%
                          </div>
                        </td>
                        <td className="text-center p-2">
                          <div className="flex items-center justify-center">
                            {getStatusBadge(commitment.status)}
                            {getEscalationBadge(commitment.escalationLevel)}
                          </div>
                        </td>
                        <td className="text-center p-2">
                          <div className="flex items-center justify-center gap-1">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => setSelectedCommitment(commitment)}
                            >
                              <FileText className="w-4 h-4" />
                            </Button>
                            <Select
                              value={commitment.status}
                              onValueChange={(status) => updateCommitmentStatus(commitment.id, status as PaymentCommitment['status'])}
                            >
                              <SelectTrigger className="w-24 h-8">
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="pending">Pending</SelectItem>
                                <SelectItem value="confirmed">Confirmed</SelectItem>
                                <SelectItem value="partial">Partial</SelectItem>
                                <SelectItem value="fulfilled">Fulfilled</SelectItem>
                                <SelectItem value="broken">Broken</SelectItem>
                                <SelectItem value="renegotiated">Renegotiated</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="followups" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Follow-up Actions</CardTitle>
              <CardDescription>Scheduled follow-up activities for payment commitments</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {commitments.flatMap(c => c.followUpActions).map((action) => (
                  <div key={action.id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="space-y-1">
                      <div className="font-medium">{action.description}</div>
                      <div className="text-sm text-muted-foreground">
                        {format(action.scheduledDate, 'MMM dd, yyyy')} • Assigned to: {action.assignedTo}
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      {getStatusBadge(action.status as any)}
                      <Button variant="ghost" size="sm">
                        <Edit className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Commitment Status Distribution</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {Object.entries(
                    commitments.reduce((acc, c) => {
                      acc[c.status] = (acc[c.status] || 0) + 1;
                      return acc;
                    }, {} as Record<string, number>)
                  ).map(([status, count]) => (
                    <div key={status} className="flex justify-between">
                      <span className="capitalize">{status}</span>
                      <span className="font-medium">{count}</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Payment Methods</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {Object.entries(
                    commitments.reduce((acc, c) => {
                      acc[c.paymentMethod] = (acc[c.paymentMethod] || 0) + 1;
                      return acc;
                    }, {} as Record<string, number>)
                  ).map(([method, count]) => (
                    <div key={method} className="flex justify-between">
                      <span>{method}</span>
                      <span className="font-medium">{count}</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>

      {/* New Commitment Dialog */}
      <Dialog open={showNewCommitment} onOpenChange={setShowNewCommitment}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Create Payment Commitment</DialogTitle>
            <DialogDescription>
              Record a new customer payment commitment
            </DialogDescription>
          </DialogHeader>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="customer-name">Customer Name</Label>
              <Input
                id="customer-name"
                value={newCommitment.customerName}
                onChange={(e) => setNewCommitment(prev => ({ ...prev, customerName: e.target.value }))}
              />
            </div>
            <div>
              <Label htmlFor="invoice-number">Invoice Number</Label>
              <Input
                id="invoice-number"
                value={newCommitment.invoiceNumber}
                onChange={(e) => setNewCommitment(prev => ({ ...prev, invoiceNumber: e.target.value }))}
              />
            </div>
            <div>
              <Label htmlFor="original-amount">Original Amount</Label>
              <Input
                id="original-amount"
                type="number"
                value={newCommitment.originalAmount}
                onChange={(e) => setNewCommitment(prev => ({ ...prev, originalAmount: Number(e.target.value) }))}
              />
            </div>
            <div>
              <Label htmlFor="commitment-amount">Commitment Amount</Label>
              <Input
                id="commitment-amount"
                type="number"
                value={newCommitment.commitmentAmount}
                onChange={(e) => setNewCommitment(prev => ({ ...prev, commitmentAmount: Number(e.target.value) }))}
              />
            </div>
            <div>
              <Label htmlFor="payment-method">Payment Method</Label>
              <Select
                value={newCommitment.paymentMethod}
                onValueChange={(value) => setNewCommitment(prev => ({ ...prev, paymentMethod: value }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select method" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Wire Transfer">Wire Transfer</SelectItem>
                  <SelectItem value="ACH">ACH</SelectItem>
                  <SelectItem value="Credit Card">Credit Card</SelectItem>
                  <SelectItem value="Check">Check</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="confidence">Confidence Level</Label>
              <Input
                id="confidence"
                type="number"
                min="0"
                max="100"
                value={newCommitment.confidence}
                onChange={(e) => setNewCommitment(prev => ({ ...prev, confidence: Number(e.target.value) }))}
              />
            </div>
          </div>
          <div>
            <Label htmlFor="notes">Notes</Label>
            <Textarea
              id="notes"
              value={newCommitment.notes}
              onChange={(e) => setNewCommitment(prev => ({ ...prev, notes: e.target.value }))}
              rows={3}
            />
          </div>
          <div className="flex justify-end gap-2">
            <Button variant="outline" onClick={() => setShowNewCommitment(false)}>
              Cancel
            </Button>
            <Button onClick={createCommitment}>
              Create Commitment
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};
