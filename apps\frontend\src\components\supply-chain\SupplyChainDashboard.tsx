import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Ship, Truck, Package, AlertTriangle, CheckCircle, Clock, 
  TrendingUp, TrendingDown, Globe, Settings, BarChart3,
  Container, Navigation, Anchor, Zap, Target, Shield
} from 'lucide-react';
import { ContainerTrackingDashboard } from './ContainerTrackingDashboard';
import { SupplyChainRulesConfig } from './SupplyChainRulesConfig';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, <PERSON><PERSON><PERSON>, Pie, Cell, LineChart, Line } from 'recharts';

interface SupplyChainMetrics {
  totalShipments: number;
  inTransit: number;
  onTimeDelivery: number;
  exceptions: number;
  averageTransitTime: number;
  costPerShipment: number;
  customerSatisfaction: number;
  inventoryTurnover: number;
}

interface ExceptionSummary {
  type: string;
  count: number;
  severity: 'low' | 'medium' | 'high' | 'critical';
  trend: 'up' | 'down' | 'stable';
}

interface PerformanceData {
  month: string;
  onTime: number;
  delayed: number;
  exceptions: number;
  cost: number;
}

export const SupplyChainDashboard: React.FC = () => {
  const [metrics, setMetrics] = useState<SupplyChainMetrics | null>(null);
  const [exceptions, setExceptions] = useState<ExceptionSummary[]>([]);
  const [performanceData, setPerformanceData] = useState<PerformanceData[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    setLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setMetrics({
        totalShipments: 1247,
        inTransit: 342,
        onTimeDelivery: 87.3,
        exceptions: 23,
        averageTransitTime: 18.5,
        costPerShipment: 1250,
        customerSatisfaction: 4.2,
        inventoryTurnover: 8.7
      });

      setExceptions([
        { type: 'Weather Delays', count: 8, severity: 'medium', trend: 'up' },
        { type: 'Port Congestion', count: 5, severity: 'high', trend: 'down' },
        { type: 'Documentation Issues', count: 4, severity: 'medium', trend: 'stable' },
        { type: 'Customs Delays', count: 3, severity: 'high', trend: 'down' },
        { type: 'Vessel Breakdown', count: 2, severity: 'critical', trend: 'stable' },
        { type: 'Route Changes', count: 1, severity: 'low', trend: 'down' }
      ]);

      setPerformanceData([
        { month: 'Jan', onTime: 85, delayed: 15, exceptions: 18, cost: 1200 },
        { month: 'Feb', onTime: 88, delayed: 12, exceptions: 15, cost: 1180 },
        { month: 'Mar', onTime: 82, delayed: 18, exceptions: 22, cost: 1300 },
        { month: 'Apr', onTime: 90, delayed: 10, exceptions: 12, cost: 1150 },
        { month: 'May', onTime: 87, delayed: 13, exceptions: 16, cost: 1220 },
        { month: 'Jun', onTime: 89, delayed: 11, exceptions: 14, cost: 1190 }
      ]);

    } catch (error) {
      console.error('Failed to load dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'text-red-600';
      case 'high': return 'text-orange-600';
      case 'medium': return 'text-yellow-600';
      case 'low': return 'text-green-600';
      default: return 'text-gray-600';
    }
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up': return <TrendingUp className="w-4 h-4 text-red-500" />;
      case 'down': return <TrendingDown className="w-4 h-4 text-green-500" />;
      default: return <div className="w-4 h-4" />;
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'];

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="mt-2 text-muted-foreground">Loading supply chain dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Supply Chain Intelligence Dashboard</h1>
          <p className="text-muted-foreground">Comprehensive view of supply chain operations and performance</p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline">
            <BarChart3 className="w-4 h-4 mr-2" />
            Analytics
          </Button>
          <Button variant="outline">
            <Settings className="w-4 h-4 mr-2" />
            Configure
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      {metrics && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Shipments</CardTitle>
              <Container className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{metrics.totalShipments.toLocaleString()}</div>
              <p className="text-xs text-muted-foreground">
                {metrics.inTransit} in transit
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">On-Time Delivery</CardTitle>
              <CheckCircle className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">{metrics.onTimeDelivery}%</div>
              <p className="text-xs text-muted-foreground">
                Target: 90%
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active Exceptions</CardTitle>
              <AlertTriangle className="h-4 w-4 text-destructive" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-destructive">{metrics.exceptions}</div>
              <p className="text-xs text-muted-foreground">
                Require attention
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Avg Transit Time</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{metrics.averageTransitTime} days</div>
              <p className="text-xs text-muted-foreground">
                Target: 20 days
              </p>
            </CardContent>
          </Card>
        </div>
      )}

      <Tabs defaultValue="overview" className="w-full">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="tracking">Container Tracking</TabsTrigger>
          <TabsTrigger value="exceptions">Exception Management</TabsTrigger>
          <TabsTrigger value="rules">Rules Configuration</TabsTrigger>
          <TabsTrigger value="analytics">Advanced Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Performance Trends */}
            <Card>
              <CardHeader>
                <CardTitle>Performance Trends</CardTitle>
                <CardDescription>Monthly on-time delivery and exception trends</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <LineChart data={performanceData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis />
                    <Tooltip />
                    <Line type="monotone" dataKey="onTime" stroke="#22c55e" strokeWidth={2} name="On-Time %" />
                    <Line type="monotone" dataKey="exceptions" stroke="#ef4444" strokeWidth={2} name="Exceptions" />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            {/* Exception Summary */}
            <Card>
              <CardHeader>
                <CardTitle>Exception Summary</CardTitle>
                <CardDescription>Current supply chain exceptions by type</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {exceptions.map((exception, index) => (
                    <div key={exception.type} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center gap-3">
                        <div className={`w-3 h-3 rounded-full ${
                          exception.severity === 'critical' ? 'bg-red-500' :
                          exception.severity === 'high' ? 'bg-orange-500' :
                          exception.severity === 'medium' ? 'bg-yellow-500' :
                          'bg-green-500'
                        }`} />
                        <div>
                          <div className="font-medium">{exception.type}</div>
                          <div className={`text-sm ${getSeverityColor(exception.severity)}`}>
                            {exception.severity.charAt(0).toUpperCase() + exception.severity.slice(1)} severity
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <span className="font-bold">{exception.count}</span>
                        {getTrendIcon(exception.trend)}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Cost Analysis */}
            <Card>
              <CardHeader>
                <CardTitle>Cost Analysis</CardTitle>
                <CardDescription>Monthly shipping costs and trends</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart data={performanceData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis />
                    <Tooltip formatter={(value) => formatCurrency(value as number)} />
                    <Bar dataKey="cost" fill="#3b82f6" name="Cost per Shipment" />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            {/* Key Performance Indicators */}
            <Card>
              <CardHeader>
                <CardTitle>Key Performance Indicators</CardTitle>
                <CardDescription>Additional supply chain metrics</CardDescription>
              </CardHeader>
              <CardContent>
                {metrics && (
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span>Cost per Shipment</span>
                      <span className="font-bold">{formatCurrency(metrics.costPerShipment)}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span>Customer Satisfaction</span>
                      <span className="font-bold">{metrics.customerSatisfaction}/5.0</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span>Inventory Turnover</span>
                      <span className="font-bold">{metrics.inventoryTurnover}x</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span>Shipments in Transit</span>
                      <span className="font-bold">{metrics.inTransit}</span>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Recent Alerts */}
          <Card>
            <CardHeader>
              <CardTitle>Recent Alerts & Notifications</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <Alert>
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>
                    <strong>High Priority:</strong> Container MSCU1234567 delayed due to port congestion in Los Angeles. 
                    Estimated 2-day delay affecting 3 customer orders.
                  </AlertDescription>
                </Alert>
                <Alert>
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>
                    <strong>Documentation:</strong> Missing bill of lading for shipment SHIP-2024-045. 
                    Customs clearance on hold.
                  </AlertDescription>
                </Alert>
                <Alert>
                  <CheckCircle className="h-4 w-4" />
                  <AlertDescription>
                    <strong>Success:</strong> AI rule "High Priority Customer Expedite" successfully processed 
                    12 shipments this week with 95% on-time delivery.
                  </AlertDescription>
                </Alert>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="tracking">
          <ContainerTrackingDashboard />
        </TabsContent>

        <TabsContent value="exceptions" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Exception Management</CardTitle>
              <CardDescription>Monitor and manage supply chain exceptions</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <Card>
                  <CardHeader className="pb-3">
                    <CardTitle className="text-lg">Critical Exceptions</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-3xl font-bold text-red-600">
                      {exceptions.filter(e => e.severity === 'critical').reduce((sum, e) => sum + e.count, 0)}
                    </div>
                    <p className="text-sm text-muted-foreground">Immediate attention required</p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-3">
                    <CardTitle className="text-lg">High Priority</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-3xl font-bold text-orange-600">
                      {exceptions.filter(e => e.severity === 'high').reduce((sum, e) => sum + e.count, 0)}
                    </div>
                    <p className="text-sm text-muted-foreground">Action needed within 24h</p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-3">
                    <CardTitle className="text-lg">Medium Priority</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-3xl font-bold text-yellow-600">
                      {exceptions.filter(e => e.severity === 'medium').reduce((sum, e) => sum + e.count, 0)}
                    </div>
                    <p className="text-sm text-muted-foreground">Monitor closely</p>
                  </CardContent>
                </Card>
              </div>

              <div className="mt-6">
                <h3 className="text-lg font-semibold mb-4">Exception Details</h3>
                <div className="space-y-3">
                  {exceptions.map((exception, index) => (
                    <div key={exception.type} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center gap-4">
                        <Badge variant={
                          exception.severity === 'critical' ? 'destructive' :
                          exception.severity === 'high' ? 'destructive' :
                          exception.severity === 'medium' ? 'default' :
                          'secondary'
                        }>
                          {exception.severity}
                        </Badge>
                        <div>
                          <div className="font-medium">{exception.type}</div>
                          <div className="text-sm text-muted-foreground">
                            {exception.count} active case{exception.count !== 1 ? 's' : ''}
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        {getTrendIcon(exception.trend)}
                        <Button variant="outline" size="sm">
                          View Details
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="rules">
          <SupplyChainRulesConfig />
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Exception Distribution</CardTitle>
                <CardDescription>Breakdown of exceptions by type</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Pie
                      data={exceptions}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ type, count }) => `${type}: ${count}`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="count"
                    >
                      {exceptions.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Performance Comparison</CardTitle>
                <CardDescription>On-time vs delayed shipments by month</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart data={performanceData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis />
                    <Tooltip />
                    <Bar dataKey="onTime" stackId="a" fill="#22c55e" name="On-Time" />
                    <Bar dataKey="delayed" stackId="a" fill="#ef4444" name="Delayed" />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Advanced Analytics</CardTitle>
              <CardDescription>AI-powered insights and predictions</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="text-center p-4 border rounded-lg">
                  <Zap className="w-8 h-8 mx-auto mb-2 text-yellow-600" />
                  <div className="text-2xl font-bold">15</div>
                  <div className="text-sm text-muted-foreground">AI Rules Active</div>
                </div>
                <div className="text-center p-4 border rounded-lg">
                  <Target className="w-8 h-8 mx-auto mb-2 text-green-600" />
                  <div className="text-2xl font-bold">92%</div>
                  <div className="text-sm text-muted-foreground">Prediction Accuracy</div>
                </div>
                <div className="text-center p-4 border rounded-lg">
                  <Shield className="w-8 h-8 mx-auto mb-2 text-blue-600" />
                  <div className="text-2xl font-bold">$125K</div>
                  <div className="text-sm text-muted-foreground">Cost Savings (YTD)</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};
