import { AIService } from './aiService';
import { AppError } from '@ar-scia/shared-utils';

export interface EmailResponse {
  id: string;
  emailId: string;
  customerId: string;
  invoiceId: string;
  responseDate: Date;
  responseType: 'email' | 'phone' | 'portal' | 'webhook';
  rawContent: string;
  processedContent: ProcessedResponse;
  customerIntention: CustomerIntention;
  followUpActions: FollowUpAction[];
  status: 'pending' | 'processed' | 'escalated' | 'resolved';
}

export interface ProcessedResponse {
  sentiment: 'positive' | 'negative' | 'neutral';
  confidence: number;
  keyPoints: string[];
  entities: {
    dates: string[];
    amounts: string[];
    paymentMethods: string[];
    contactInfo: string[];
  };
  urgency: 'low' | 'medium' | 'high' | 'critical';
  category: 'payment_commitment' | 'dispute' | 'request_extension' | 'payment_confirmation' | 'complaint' | 'other';
}

export interface CustomerIntention {
  type: 'will_pay' | 'dispute' | 'cannot_pay' | 'needs_extension' | 'already_paid' | 'unclear';
  confidence: number;
  commitmentDate?: string;
  proposedAmount?: number;
  paymentMethod?: string;
  reasonForDelay?: string;
  disputeReason?: string;
  supportingDocuments?: string[];
}

export interface FollowUpAction {
  id: string;
  type: 'send_email' | 'schedule_call' | 'update_account' | 'escalate' | 'close_case' | 'request_documents';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  dueDate: Date;
  description: string;
  assignedTo?: string;
  status: 'pending' | 'in_progress' | 'completed' | 'cancelled';
  automatable: boolean;
}

export interface WebhookPayload {
  emailId: string;
  customerId: string;
  invoiceId: string;
  responseContent: string;
  responseType: string;
  timestamp: string;
  metadata?: Record<string, any>;
}

export class ResponseService {
  private aiService: AIService;

  constructor() {
    this.aiService = new AIService();
  }

  async processEmailResponse(webhookPayload: WebhookPayload): Promise<EmailResponse> {
    try {
      const responseId = `resp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      
      // Process the response content with AI
      const processedContent = await this.analyzeResponseContent(webhookPayload.responseContent);
      
      // Determine customer intention
      const customerIntention = await this.extractCustomerIntention(
        webhookPayload.responseContent,
        processedContent
      );
      
      // Generate follow-up actions
      const followUpActions = await this.generateFollowUpActions(
        processedContent,
        customerIntention,
        webhookPayload
      );

      const emailResponse: EmailResponse = {
        id: responseId,
        emailId: webhookPayload.emailId,
        customerId: webhookPayload.customerId,
        invoiceId: webhookPayload.invoiceId,
        responseDate: new Date(webhookPayload.timestamp),
        responseType: webhookPayload.responseType as any,
        rawContent: webhookPayload.responseContent,
        processedContent,
        customerIntention,
        followUpActions,
        status: this.determineResponseStatus(customerIntention, processedContent)
      };

      // In a real implementation, save to database
      console.log('Email response processed:', emailResponse);

      return emailResponse;
    } catch (error) {
      console.error('Response processing error:', error);
      throw new AppError('Failed to process email response', 500);
    }
  }

  private async analyzeResponseContent(content: string): Promise<ProcessedResponse> {
    try {
      const prompt = `
        Analyze this customer email response to an accounts receivable communication:
        
        "${content}"
        
        Extract and analyze:
        1. Sentiment (positive, negative, neutral)
        2. Key points mentioned
        3. Important entities (dates, amounts, payment methods, contact info)
        4. Urgency level
        5. Response category
        
        Return analysis in this JSON format:
        {
          "sentiment": "positive|negative|neutral",
          "confidence": 0.95,
          "keyPoints": ["list of key points"],
          "entities": {
            "dates": ["extracted dates"],
            "amounts": ["extracted amounts"],
            "paymentMethods": ["mentioned payment methods"],
            "contactInfo": ["phone numbers, emails, etc."]
          },
          "urgency": "low|medium|high|critical",
          "category": "payment_commitment|dispute|request_extension|payment_confirmation|complaint|other"
        }
      `;

      const result = await this.aiService.processNaturalLanguageQuery(prompt);
      const jsonMatch = result.match(/\{[\s\S]*\}/);
      
      if (!jsonMatch) {
        throw new Error('Invalid AI response format');
      }

      return JSON.parse(jsonMatch[0]);
    } catch (error) {
      console.error('Content analysis error:', error);
      // Return default analysis if AI fails
      return {
        sentiment: 'neutral',
        confidence: 0.5,
        keyPoints: ['Response received'],
        entities: { dates: [], amounts: [], paymentMethods: [], contactInfo: [] },
        urgency: 'medium',
        category: 'other'
      };
    }
  }

  private async extractCustomerIntention(
    content: string,
    processedContent: ProcessedResponse
  ): Promise<CustomerIntention> {
    try {
      const prompt = `
        Based on this customer response and analysis, determine the customer's intention:
        
        Response: "${content}"
        Analysis: ${JSON.stringify(processedContent)}
        
        Determine:
        1. Primary intention type
        2. Confidence level
        3. Specific commitments or details
        
        Return in this JSON format:
        {
          "type": "will_pay|dispute|cannot_pay|needs_extension|already_paid|unclear",
          "confidence": 0.95,
          "commitmentDate": "2024-01-15" (if mentioned),
          "proposedAmount": 1000.00 (if mentioned),
          "paymentMethod": "credit card" (if mentioned),
          "reasonForDelay": "reason" (if applicable),
          "disputeReason": "reason" (if dispute),
          "supportingDocuments": ["list if mentioned"]
        }
      `;

      const result = await this.aiService.processNaturalLanguageQuery(prompt);
      const jsonMatch = result.match(/\{[\s\S]*\}/);
      
      if (!jsonMatch) {
        throw new Error('Invalid AI response format');
      }

      return JSON.parse(jsonMatch[0]);
    } catch (error) {
      console.error('Intention extraction error:', error);
      return {
        type: 'unclear',
        confidence: 0.5
      };
    }
  }

  private async generateFollowUpActions(
    processedContent: ProcessedResponse,
    customerIntention: CustomerIntention,
    webhookPayload: WebhookPayload
  ): Promise<FollowUpAction[]> {
    const actions: FollowUpAction[] = [];
    const baseDate = new Date();

    // Generate actions based on intention type
    switch (customerIntention.type) {
      case 'will_pay':
        if (customerIntention.commitmentDate) {
          actions.push({
            id: `action_${Date.now()}_1`,
            type: 'schedule_call',
            priority: 'medium',
            dueDate: new Date(customerIntention.commitmentDate),
            description: `Follow up on payment commitment for ${customerIntention.commitmentDate}`,
            status: 'pending',
            automatable: true
          });
        }
        actions.push({
          id: `action_${Date.now()}_2`,
          type: 'update_account',
          priority: 'medium',
          dueDate: new Date(baseDate.getTime() + 24 * 60 * 60 * 1000),
          description: 'Update account with payment commitment details',
          status: 'pending',
          automatable: true
        });
        break;

      case 'dispute':
        actions.push({
          id: `action_${Date.now()}_3`,
          type: 'escalate',
          priority: 'high',
          dueDate: new Date(baseDate.getTime() + 4 * 60 * 60 * 1000),
          description: `Escalate dispute: ${customerIntention.disputeReason}`,
          status: 'pending',
          automatable: false
        });
        if (customerIntention.supportingDocuments?.length) {
          actions.push({
            id: `action_${Date.now()}_4`,
            type: 'request_documents',
            priority: 'high',
            dueDate: new Date(baseDate.getTime() + 24 * 60 * 60 * 1000),
            description: 'Review supporting documents provided by customer',
            status: 'pending',
            automatable: false
          });
        }
        break;

      case 'cannot_pay':
        actions.push({
          id: `action_${Date.now()}_5`,
          type: 'schedule_call',
          priority: 'high',
          dueDate: new Date(baseDate.getTime() + 24 * 60 * 60 * 1000),
          description: 'Schedule call to discuss payment arrangements',
          status: 'pending',
          automatable: false
        });
        break;

      case 'needs_extension':
        actions.push({
          id: `action_${Date.now()}_6`,
          type: 'send_email',
          priority: 'medium',
          dueDate: new Date(baseDate.getTime() + 4 * 60 * 60 * 1000),
          description: 'Send payment extension options',
          status: 'pending',
          automatable: true
        });
        break;

      case 'already_paid':
        actions.push({
          id: `action_${Date.now()}_7`,
          type: 'update_account',
          priority: 'high',
          dueDate: new Date(baseDate.getTime() + 2 * 60 * 60 * 1000),
          description: 'Verify payment status and update account',
          status: 'pending',
          automatable: true
        });
        break;

      default:
        actions.push({
          id: `action_${Date.now()}_8`,
          type: 'schedule_call',
          priority: 'medium',
          dueDate: new Date(baseDate.getTime() + 24 * 60 * 60 * 1000),
          description: 'Call customer to clarify response and next steps',
          status: 'pending',
          automatable: false
        });
    }

    // Add urgency-based actions
    if (processedContent.urgency === 'critical') {
      actions.push({
        id: `action_${Date.now()}_urgent`,
        type: 'escalate',
        priority: 'urgent',
        dueDate: new Date(baseDate.getTime() + 60 * 60 * 1000),
        description: 'Immediate escalation due to critical urgency',
        status: 'pending',
        automatable: false
      });
    }

    return actions;
  }

  private determineResponseStatus(
    intention: CustomerIntention,
    processed: ProcessedResponse
  ): 'pending' | 'processed' | 'escalated' | 'resolved' {
    if (intention.type === 'already_paid') {
      return 'resolved';
    }
    
    if (intention.type === 'dispute' || processed.urgency === 'critical') {
      return 'escalated';
    }
    
    if (intention.confidence > 0.8) {
      return 'processed';
    }
    
    return 'pending';
  }

  async getResponseById(responseId: string): Promise<EmailResponse | null> {
    // In a real implementation, fetch from database
    console.log(`Fetching response: ${responseId}`);
    return null;
  }

  async getResponsesByCustomer(customerId: string): Promise<EmailResponse[]> {
    // In a real implementation, fetch from database
    console.log(`Fetching responses for customer: ${customerId}`);
    return [];
  }

  async getResponsesByInvoice(invoiceId: string): Promise<EmailResponse[]> {
    // In a real implementation, fetch from database
    console.log(`Fetching responses for invoice: ${invoiceId}`);
    return [];
  }

  async updateFollowUpAction(actionId: string, updates: Partial<FollowUpAction>): Promise<FollowUpAction> {
    // In a real implementation, update in database
    console.log(`Updating action ${actionId}:`, updates);
    throw new AppError('Not implemented', 501);
  }

  async getFollowUpActions(filters?: {
    status?: string;
    priority?: string;
    assignedTo?: string;
    dueDate?: string;
  }): Promise<FollowUpAction[]> {
    // In a real implementation, fetch from database with filters
    console.log('Fetching follow-up actions with filters:', filters);
    return [];
  }

  // Webhook validation
  validateWebhookPayload(payload: any): WebhookPayload {
    const required = ['emailId', 'customerId', 'invoiceId', 'responseContent', 'responseType', 'timestamp'];
    
    for (const field of required) {
      if (!payload[field]) {
        throw new AppError(`Missing required field: ${field}`, 400);
      }
    }

    return payload as WebhookPayload;
  }

  // Generate response summary for reporting
  generateResponseSummary(responses: EmailResponse[]): {
    totalResponses: number;
    byIntention: Record<string, number>;
    bySentiment: Record<string, number>;
    averageConfidence: number;
    pendingActions: number;
  } {
    const summary = {
      totalResponses: responses.length,
      byIntention: {} as Record<string, number>,
      bySentiment: {} as Record<string, number>,
      averageConfidence: 0,
      pendingActions: 0
    };

    responses.forEach(response => {
      // Count by intention
      summary.byIntention[response.customerIntention.type] = 
        (summary.byIntention[response.customerIntention.type] || 0) + 1;
      
      // Count by sentiment
      summary.bySentiment[response.processedContent.sentiment] = 
        (summary.bySentiment[response.processedContent.sentiment] || 0) + 1;
      
      // Count pending actions
      summary.pendingActions += response.followUpActions.filter(a => a.status === 'pending').length;
    });

    // Calculate average confidence
    if (responses.length > 0) {
      summary.averageConfidence = responses.reduce(
        (sum, r) => sum + r.customerIntention.confidence, 0
      ) / responses.length;
    }

    return summary;
  }
}

export const responseService = new ResponseService();

// Response capture routes will be added separately
