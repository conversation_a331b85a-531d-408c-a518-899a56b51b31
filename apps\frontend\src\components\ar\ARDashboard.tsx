import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer,
  PieChart, Pie, Cell, LineChart, Line, Area, AreaChart
} from 'recharts';
import { 
  DollarSign, TrendingUp, TrendingDown, Clock, Mail, Phone, 
  Download, Filter, Calendar, Users, AlertTriangle, CheckCircle,
  FileText, Eye, Send, MoreHorizontal
} from 'lucide-react';
import { EmailManagement } from './EmailManagement';

interface AgingBucket {
  range: string;
  count: number;
  amount: number;
  percentage: number;
}

interface ARMetrics {
  totalOutstanding: number;
  overdueAmount: number;
  currentAmount: number;
  averageDaysOutstanding: number;
  collectionEfficiency: number;
  totalInvoices: number;
  overdueInvoices: number;
}

interface Invoice {
  id: string;
  number: string;
  customerId: string;
  customerName: string;
  amount: number;
  dueDate: string;
  daysPastDue: number;
  status: 'current' | 'overdue' | 'disputed' | 'paid';
  lastContact?: string;
  nextAction?: string;
  priority: 'low' | 'medium' | 'high' | 'critical';
}

interface ResponseAnalytics {
  totalResponses: number;
  responseRate: number;
  averageResponseTime: number;
  intentionBreakdown: Record<string, number>;
  sentimentBreakdown: Record<string, number>;
}

export const ARDashboard: React.FC = () => {
  const [metrics, setMetrics] = useState<ARMetrics | null>(null);
  const [agingData, setAgingData] = useState<AgingBucket[]>([]);
  const [invoices, setInvoices] = useState<Invoice[]>([]);
  const [responseAnalytics, setResponseAnalytics] = useState<ResponseAnalytics | null>(null);
  const [loading, setLoading] = useState(true);
  const [selectedInvoice, setSelectedInvoice] = useState<Invoice | null>(null);
  const [dateRange, setDateRange] = useState('30');
  const [filterStatus, setFilterStatus] = useState('all');

  useEffect(() => {
    loadDashboardData();
  }, [dateRange, filterStatus]);

  const loadDashboardData = async () => {
    setLoading(true);
    try {
      // Simulate API calls - replace with actual service calls
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Mock data
      setMetrics({
        totalOutstanding: 2450000,
        overdueAmount: 890000,
        currentAmount: 1560000,
        averageDaysOutstanding: 28,
        collectionEfficiency: 92.5,
        totalInvoices: 156,
        overdueInvoices: 42
      });

      setAgingData([
        { range: 'Current', count: 114, amount: 1560000, percentage: 63.7 },
        { range: '1-30 days', count: 28, amount: 420000, percentage: 17.1 },
        { range: '31-60 days', count: 8, amount: 280000, percentage: 11.4 },
        { range: '61-90 days', count: 4, amount: 120000, percentage: 4.9 },
        { range: '90+ days', count: 2, amount: 70000, percentage: 2.9 }
      ]);

      setInvoices([
        {
          id: '1',
          number: 'INV-2024-001',
          customerId: 'cust-1',
          customerName: 'Acme Corporation',
          amount: 25000,
          dueDate: '2024-01-15',
          daysPastDue: 45,
          status: 'overdue',
          lastContact: '2024-01-20',
          nextAction: 'Send final notice',
          priority: 'high'
        },
        {
          id: '2',
          number: 'INV-2024-002',
          customerId: 'cust-2',
          customerName: 'Global Industries',
          amount: 18500,
          dueDate: '2024-02-01',
          daysPastDue: 15,
          status: 'overdue',
          lastContact: '2024-02-10',
          nextAction: 'Schedule call',
          priority: 'medium'
        },
        {
          id: '3',
          number: 'INV-2024-003',
          customerId: 'cust-3',
          customerName: 'Tech Solutions Ltd',
          amount: 12000,
          dueDate: '2024-02-15',
          daysPastDue: 0,
          status: 'current',
          priority: 'low'
        }
      ]);

      setResponseAnalytics({
        totalResponses: 128,
        responseRate: 68.5,
        averageResponseTime: 2.3,
        intentionBreakdown: {
          'will_pay': 45,
          'dispute': 12,
          'needs_extension': 28,
          'already_paid': 18,
          'unclear': 25
        },
        sentimentBreakdown: {
          'positive': 52,
          'neutral': 48,
          'negative': 28
        }
      });

    } catch (error) {
      console.error('Failed to load dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  const getStatusBadge = (status: string) => {
    const variants = {
      current: 'default',
      overdue: 'destructive',
      disputed: 'secondary',
      paid: 'success'
    } as const;

    return (
      <Badge variant={variants[status as keyof typeof variants] || 'default'}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    );
  };

  const getPriorityBadge = (priority: string) => {
    const variants = {
      low: 'default',
      medium: 'secondary',
      high: 'destructive',
      critical: 'destructive'
    } as const;

    return (
      <Badge variant={variants[priority as keyof typeof variants] || 'default'}>
        {priority.charAt(0).toUpperCase() + priority.slice(1)}
      </Badge>
    );
  };

  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'];

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="mt-2 text-muted-foreground">Loading AR Dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Accounts Receivable Dashboard</h1>
          <p className="text-muted-foreground">Monitor outstanding invoices and collection activities</p>
        </div>
        <div className="flex items-center gap-2">
          <Select value={dateRange} onValueChange={setDateRange}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7">Last 7 days</SelectItem>
              <SelectItem value="30">Last 30 days</SelectItem>
              <SelectItem value="90">Last 90 days</SelectItem>
              <SelectItem value="365">Last year</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline">
            <Download className="w-4 h-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      {metrics && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Outstanding</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatCurrency(metrics.totalOutstanding)}</div>
              <p className="text-xs text-muted-foreground">
                {metrics.totalInvoices} invoices
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Overdue Amount</CardTitle>
              <AlertTriangle className="h-4 w-4 text-destructive" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-destructive">{formatCurrency(metrics.overdueAmount)}</div>
              <p className="text-xs text-muted-foreground">
                {metrics.overdueInvoices} overdue invoices
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Avg Days Outstanding</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{metrics.averageDaysOutstanding}</div>
              <p className="text-xs text-muted-foreground">
                Target: 30 days
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Collection Efficiency</CardTitle>
              <TrendingUp className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">{metrics.collectionEfficiency}%</div>
              <p className="text-xs text-muted-foreground">
                +2.3% from last month
              </p>
            </CardContent>
          </Card>
        </div>
      )}

      <Tabs defaultValue="aging" className="w-full">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="aging">Aging Report</TabsTrigger>
          <TabsTrigger value="invoices">Invoice Management</TabsTrigger>
          <TabsTrigger value="responses">Response Analytics</TabsTrigger>
          <TabsTrigger value="email">Email Management</TabsTrigger>
          <TabsTrigger value="trends">Trends</TabsTrigger>
        </TabsList>

        <TabsContent value="aging" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Aging Chart */}
            <Card>
              <CardHeader>
                <CardTitle>Aging Analysis</CardTitle>
                <CardDescription>Outstanding amounts by aging bucket</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart data={agingData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="range" />
                    <YAxis tickFormatter={(value) => `$${(value / 1000).toFixed(0)}K`} />
                    <Tooltip formatter={(value) => formatCurrency(value as number)} />
                    <Bar dataKey="amount" fill="#8884d8" />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            {/* Aging Pie Chart */}
            <Card>
              <CardHeader>
                <CardTitle>Aging Distribution</CardTitle>
                <CardDescription>Percentage breakdown by aging bucket</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Pie
                      data={agingData}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ range, percentage }) => `${range}: ${percentage}%`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="amount"
                    >
                      {agingData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip formatter={(value) => formatCurrency(value as number)} />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>

          {/* Aging Table */}
          <Card>
            <CardHeader>
              <CardTitle>Aging Summary</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b">
                      <th className="text-left p-2">Aging Bucket</th>
                      <th className="text-right p-2">Count</th>
                      <th className="text-right p-2">Amount</th>
                      <th className="text-right p-2">Percentage</th>
                    </tr>
                  </thead>
                  <tbody>
                    {agingData.map((bucket, index) => (
                      <tr key={index} className="border-b">
                        <td className="p-2 font-medium">{bucket.range}</td>
                        <td className="text-right p-2">{bucket.count}</td>
                        <td className="text-right p-2">{formatCurrency(bucket.amount)}</td>
                        <td className="text-right p-2">{bucket.percentage}%</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="invoices" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Invoice Management</CardTitle>
              <CardDescription>Manage outstanding invoices and collection activities</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex items-center gap-4 mb-4">
                <div className="flex items-center gap-2">
                  <Label htmlFor="status-filter">Status:</Label>
                  <Select value={filterStatus} onValueChange={setFilterStatus}>
                    <SelectTrigger className="w-32">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All</SelectItem>
                      <SelectItem value="current">Current</SelectItem>
                      <SelectItem value="overdue">Overdue</SelectItem>
                      <SelectItem value="disputed">Disputed</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <Input placeholder="Search invoices..." className="max-w-sm" />
              </div>

              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b">
                      <th className="text-left p-2">Invoice #</th>
                      <th className="text-left p-2">Customer</th>
                      <th className="text-right p-2">Amount</th>
                      <th className="text-center p-2">Days Past Due</th>
                      <th className="text-center p-2">Status</th>
                      <th className="text-center p-2">Priority</th>
                      <th className="text-center p-2">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {invoices.map((invoice) => (
                      <tr key={invoice.id} className="border-b hover:bg-muted/50">
                        <td className="p-2 font-medium">{invoice.number}</td>
                        <td className="p-2">{invoice.customerName}</td>
                        <td className="text-right p-2">{formatCurrency(invoice.amount)}</td>
                        <td className="text-center p-2">
                          {invoice.daysPastDue > 0 ? (
                            <span className="text-destructive font-medium">{invoice.daysPastDue}</span>
                          ) : (
                            <span className="text-muted-foreground">-</span>
                          )}
                        </td>
                        <td className="text-center p-2">{getStatusBadge(invoice.status)}</td>
                        <td className="text-center p-2">{getPriorityBadge(invoice.priority)}</td>
                        <td className="text-center p-2">
                          <div className="flex items-center justify-center gap-1">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => setSelectedInvoice(invoice)}
                            >
                              <Eye className="w-4 h-4" />
                            </Button>
                            <Button variant="ghost" size="sm">
                              <Mail className="w-4 h-4" />
                            </Button>
                            <Button variant="ghost" size="sm">
                              <Phone className="w-4 h-4" />
                            </Button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="responses" className="space-y-4">
          {responseAnalytics && (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Card>
                <CardHeader>
                  <CardTitle>Response Rate</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-3xl font-bold">{responseAnalytics.responseRate}%</div>
                  <p className="text-sm text-muted-foreground">
                    {responseAnalytics.totalResponses} total responses
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Avg Response Time</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-3xl font-bold">{responseAnalytics.averageResponseTime} days</div>
                  <p className="text-sm text-muted-foreground">
                    From email sent to response
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Customer Intentions</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    {Object.entries(responseAnalytics.intentionBreakdown).map(([intention, count]) => (
                      <div key={intention} className="flex justify-between">
                        <span className="text-sm capitalize">{intention.replace('_', ' ')}</span>
                        <span className="text-sm font-medium">{count}</span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          )}
        </TabsContent>

        <TabsContent value="email" className="space-y-4">
          <EmailManagement
            customerId={selectedInvoice?.customerId}
            invoiceId={selectedInvoice?.id}
            customerData={selectedInvoice ? {
              name: selectedInvoice.customerName,
              tier: 'standard',
              email: '<EMAIL>'
            } : undefined}
            invoiceData={selectedInvoice ? {
              number: selectedInvoice.number,
              amount: selectedInvoice.amount,
              dueDate: selectedInvoice.dueDate,
              daysPastDue: selectedInvoice.daysPastDue
            } : undefined}
          />
        </TabsContent>

        <TabsContent value="trends" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Collection Trends</CardTitle>
              <CardDescription>Historical collection performance</CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <LineChart data={[
                  { month: 'Jan', collected: 850000, outstanding: 2100000 },
                  { month: 'Feb', collected: 920000, outstanding: 2050000 },
                  { month: 'Mar', collected: 880000, outstanding: 2200000 },
                  { month: 'Apr', collected: 950000, outstanding: 2150000 },
                  { month: 'May', collected: 1020000, outstanding: 2080000 },
                  { month: 'Jun', collected: 980000, outstanding: 2450000 }
                ]}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" />
                  <YAxis tickFormatter={(value) => `$${(value / 1000).toFixed(0)}K`} />
                  <Tooltip formatter={(value) => formatCurrency(value as number)} />
                  <Line type="monotone" dataKey="collected" stroke="#8884d8" name="Collected" />
                  <Line type="monotone" dataKey="outstanding" stroke="#82ca9d" name="Outstanding" />
                </LineChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};
