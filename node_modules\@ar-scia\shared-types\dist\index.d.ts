export interface User {
    id: string;
    email: string;
    firstName: string;
    lastName: string;
    isActive: boolean;
    createdAt: Date;
    updatedAt: Date;
    roles: UserRole[];
}
export interface Role {
    id: string;
    name: string;
    description?: string;
    permissions: Permission[];
}
export interface Permission {
    id: string;
    name: string;
    resource: string;
    action: string;
}
export interface UserRole {
    userId: string;
    roleId: string;
    user: User;
    role: Role;
}
export interface Customer {
    id: string;
    name: string;
    email?: string;
    phone?: string;
    address?: string;
    isActive: boolean;
    priority: Priority;
    createdAt: Date;
    updatedAt: Date;
    invoices?: Invoice[];
    shipments?: Shipment[];
    communications?: Communication[];
}
export interface Invoice {
    id: string;
    invoiceNumber: string;
    customerId: string;
    amount: number;
    dueDate: Date;
    status: InvoiceStatus;
    xeroId?: string;
    createdAt: Date;
    updatedAt: Date;
    customer?: Customer;
    payments?: Payment[];
    communications?: Communication[];
}
export interface Payment {
    id: string;
    invoiceId: string;
    amount: number;
    paymentDate: Date;
    method: PaymentMethod;
    reference?: string;
    createdAt: Date;
    invoice?: Invoice;
}
export interface Shipment {
    id: string;
    shipmentNumber: string;
    customerId: string;
    containerNumber?: string;
    status: ShipmentStatus;
    origin?: string;
    destination?: string;
    estimatedDate?: Date;
    actualDate?: Date;
    createdAt: Date;
    updatedAt: Date;
    customer?: Customer;
    trackingEvents?: TrackingEvent[];
    documents?: Document[];
    exceptions?: Exception[];
}
export interface TrackingEvent {
    id: string;
    shipmentId: string;
    location: string;
    status: string;
    description?: string;
    timestamp: Date;
    createdAt: Date;
    shipment?: Shipment;
}
export interface Document {
    id: string;
    filename: string;
    originalName: string;
    mimeType: string;
    size: number;
    path: string;
    type: DocumentType;
    shipmentId?: string;
    extractedData?: any;
    createdAt: Date;
    shipment?: Shipment;
}
export interface BusinessRule {
    id: string;
    name: string;
    description?: string;
    domain: RuleDomain;
    conditions: any;
    actions: any;
    isActive: boolean;
    priority: number;
    createdAt: Date;
    updatedAt: Date;
    exceptions?: Exception[];
}
export interface Exception {
    id: string;
    type: ExceptionType;
    severity: Severity;
    title: string;
    description: string;
    status: ExceptionStatus;
    ruleId?: string;
    shipmentId?: string;
    customerId?: string;
    data?: any;
    resolvedAt?: Date;
    createdAt: Date;
    updatedAt: Date;
    rule?: BusinessRule;
    shipment?: Shipment;
    customer?: Customer;
}
export interface Communication {
    id: string;
    type: CommunicationType;
    direction: Direction;
    subject?: string;
    content: string;
    customerId?: string;
    invoiceId?: string;
    sentAt?: Date;
    readAt?: Date;
    respondedAt?: Date;
    createdAt: Date;
    customer?: Customer;
    invoice?: Invoice;
}
export interface AuditLog {
    id: string;
    userId: string;
    action: string;
    resource: string;
    resourceId?: string;
    oldValues?: any;
    newValues?: any;
    ipAddress?: string;
    userAgent?: string;
    createdAt: Date;
    user?: User;
}
export interface Session {
    id: string;
    userId: string;
    token: string;
    expiresAt: Date;
    createdAt: Date;
    user?: User;
}
export declare enum Priority {
    LOW = "LOW",
    NORMAL = "NORMAL",
    HIGH = "HIGH",
    CRITICAL = "CRITICAL"
}
export declare enum InvoiceStatus {
    PENDING = "PENDING",
    OVERDUE = "OVERDUE",
    PAID = "PAID",
    CANCELLED = "CANCELLED"
}
export declare enum PaymentMethod {
    BANK_TRANSFER = "BANK_TRANSFER",
    CREDIT_CARD = "CREDIT_CARD",
    CHECK = "CHECK",
    CASH = "CASH",
    OTHER = "OTHER"
}
export declare enum ShipmentStatus {
    PENDING = "PENDING",
    IN_TRANSIT = "IN_TRANSIT",
    DELIVERED = "DELIVERED",
    DELAYED = "DELAYED",
    CANCELLED = "CANCELLED"
}
export declare enum DocumentType {
    PACKING_LIST = "PACKING_LIST",
    SHIPPING_NOTICE = "SHIPPING_NOTICE",
    INVOICE = "INVOICE",
    RECEIPT = "RECEIPT",
    OTHER = "OTHER"
}
export declare enum RuleDomain {
    AR = "AR",
    SUPPLY_CHAIN = "SUPPLY_CHAIN",
    CROSS_DOMAIN = "CROSS_DOMAIN"
}
export declare enum ExceptionType {
    PAYMENT_OVERDUE = "PAYMENT_OVERDUE",
    SHIPMENT_DELAY = "SHIPMENT_DELAY",
    MISSING_DOCUMENT = "MISSING_DOCUMENT",
    RULE_VIOLATION = "RULE_VIOLATION",
    SYSTEM_ERROR = "SYSTEM_ERROR"
}
export declare enum Severity {
    LOW = "LOW",
    MEDIUM = "MEDIUM",
    HIGH = "HIGH",
    CRITICAL = "CRITICAL"
}
export declare enum ExceptionStatus {
    OPEN = "OPEN",
    IN_PROGRESS = "IN_PROGRESS",
    RESOLVED = "RESOLVED",
    CLOSED = "CLOSED"
}
export declare enum CommunicationType {
    EMAIL = "EMAIL",
    SMS = "SMS",
    PHONE = "PHONE",
    SYSTEM = "SYSTEM"
}
export declare enum Direction {
    INBOUND = "INBOUND",
    OUTBOUND = "OUTBOUND"
}
export interface ApiResponse<T = any> {
    success: boolean;
    data?: T;
    error?: string;
    message?: string;
    pagination?: PaginationInfo;
}
export interface PaginationInfo {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
}
export interface PaginationParams {
    page?: number;
    limit?: number;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
    search?: string;
}
export interface LoginRequest {
    email: string;
    password: string;
}
export interface RegisterRequest {
    email: string;
    password: string;
    firstName: string;
    lastName: string;
}
export interface AuthResponse {
    user: User;
    token: string;
    expiresAt: Date;
}
export interface JwtPayload {
    userId: string;
    email: string;
    roles: string[];
    iat: number;
    exp: number;
}
export interface GeminiAIRequest {
    prompt: string;
    context?: any;
    temperature?: number;
    maxTokens?: number;
}
export interface GeminiAIResponse {
    content: string;
    confidence: number;
    usage: {
        promptTokens: number;
        completionTokens: number;
        totalTokens: number;
    };
}
export interface ParsedDocument {
    type: DocumentType;
    extractedData: any;
    confidence: number;
    errors: string[];
}
export interface AIInsight {
    type: string;
    title: string;
    description: string;
    confidence: number;
    data: any;
    recommendations: string[];
}
export interface DashboardMetrics {
    ar: ARMetrics;
    supplyChain: SupplyChainMetrics;
    overall: OverallMetrics;
}
export interface ARMetrics {
    totalOutstanding: number;
    overdueAmount: number;
    averageDSO: number;
    paymentResponseRate: number;
    monthlyCollections: number;
    agingBuckets: AgingBucket[];
}
export interface SupplyChainMetrics {
    activeShipments: number;
    delayedShipments: number;
    onTimeDeliveryRate: number;
    averageTransitTime: number;
    exceptionsCount: number;
    containerUtilization: number;
}
export interface OverallMetrics {
    totalCustomers: number;
    activeUsers: number;
    systemUptime: number;
    dataAccuracy: number;
    customerSatisfaction: number;
}
export interface AgingBucket {
    range: string;
    amount: number;
    count: number;
    percentage: number;
}
export interface CustomerFilter {
    search?: string;
    priority?: Priority[];
    isActive?: boolean;
    hasOverdueInvoices?: boolean;
    hasDelayedShipments?: boolean;
}
export interface InvoiceFilter {
    search?: string;
    status?: InvoiceStatus[];
    customerId?: string;
    dueDateFrom?: Date;
    dueDateTo?: Date;
    amountMin?: number;
    amountMax?: number;
}
export interface ShipmentFilter {
    search?: string;
    status?: ShipmentStatus[];
    customerId?: string;
    origin?: string;
    destination?: string;
    estimatedDateFrom?: Date;
    estimatedDateTo?: Date;
}
