import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, Link } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Building2,
  Mail,
  Phone,
  MapPin,
  DollarSign,
  FileText,
  AlertTriangle,
  Calendar,
  TrendingUp,
  Clock,
  ArrowLeft,
  Edit,
  CreditCard,
  Package,
  Activity
} from 'lucide-react';

interface Customer {
  id: string;
  name: string;
  email: string;
  phone: string;
  address: string;
  city: string;
  state: string;
  zipCode: string;
  totalRevenue: number;
  outstandingBalance: number;
  invoiceCount: number;
  status: 'active' | 'inactive' | 'overdue';
  creditLimit: number;
  paymentTerms: string;
  accountManager: string;
  industry: string;
  website: string;
  taxId: string;
  createdAt: string;
  lastPayment: string;
  averagePaymentDays: number;
}

interface Invoice {
  id: string;
  number: string;
  amount: number;
  status: 'paid' | 'pending' | 'overdue' | 'draft';
  dueDate: string;
  issueDate: string;
}

interface Payment {
  id: string;
  amount: number;
  date: string;
  method: string;
  reference: string;
}

interface Activity {
  id: string;
  type: 'payment' | 'invoice' | 'contact' | 'shipment';
  description: string;
  date: string;
  amount?: number;
}

// Mock data - in real app this would come from API
const mockCustomer: Customer = {
  id: '1',
  name: 'Acme Corporation',
  email: '<EMAIL>',
  phone: '(*************',
  address: '123 Business Ave',
  city: 'New York',
  state: 'NY',
  zipCode: '10001',
  totalRevenue: 125000,
  outstandingBalance: 15000,
  invoiceCount: 12,
  status: 'active',
  creditLimit: 50000,
  paymentTerms: 'Net 30',
  accountManager: 'Sarah Johnson',
  industry: 'Technology',
  website: 'https://acme.com',
  taxId: '12-3456789',
  createdAt: '2023-01-15',
  lastPayment: '2024-06-15',
  averagePaymentDays: 28
};

const mockInvoices: Invoice[] = [
  {
    id: '1',
    number: 'INV-2024-001',
    amount: 5000,
    status: 'overdue',
    dueDate: '2024-06-01',
    issueDate: '2024-05-01'
  },
  {
    id: '2',
    number: 'INV-2024-002',
    amount: 7500,
    status: 'pending',
    dueDate: '2024-07-15',
    issueDate: '2024-06-15'
  },
  {
    id: '3',
    number: 'INV-2024-003',
    amount: 3200,
    status: 'paid',
    dueDate: '2024-06-30',
    issueDate: '2024-06-01'
  }
];

const mockPayments: Payment[] = [
  {
    id: '1',
    amount: 3200,
    date: '2024-06-25',
    method: 'Bank Transfer',
    reference: 'TXN-789123'
  },
  {
    id: '2',
    amount: 8500,
    date: '2024-05-20',
    method: 'Check',
    reference: 'CHK-456789'
  }
];

const mockActivities: Activity[] = [
  {
    id: '1',
    type: 'payment',
    description: 'Payment received for INV-2024-003',
    date: '2024-06-25',
    amount: 3200
  },
  {
    id: '2',
    type: 'invoice',
    description: 'Invoice INV-2024-002 sent',
    date: '2024-06-15',
    amount: 7500
  },
  {
    id: '3',
    type: 'contact',
    description: 'Follow-up call regarding overdue payment',
    date: '2024-06-10'
  },
  {
    id: '4',
    type: 'shipment',
    description: 'Order #ORD-2024-045 shipped',
    date: '2024-06-05'
  }
];

export const CustomerDetailPage: React.FC = () => {
  const { id } = useParams();
  const [customer, setCustomer] = useState<Customer | null>(null);
  const [invoices, setInvoices] = useState<Invoice[]>([]);
  const [payments, setPayments] = useState<Payment[]>([]);
  const [activities, setActivities] = useState<Activity[]>([]);

  useEffect(() => {
    // Simulate API calls
    setCustomer(mockCustomer);
    setInvoices(mockInvoices);
    setPayments(mockPayments);
    setActivities(mockActivities);
  }, [id]);

  if (!customer) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <Building2 className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <p className="text-muted-foreground">Loading customer details...</p>
        </div>
      </div>
    );
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'overdue':
        return 'bg-red-100 text-red-800';
      case 'inactive':
        return 'bg-gray-100 text-gray-800';
      case 'paid':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'draft':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'payment':
        return <CreditCard className="h-4 w-4" />;
      case 'invoice':
        return <FileText className="h-4 w-4" />;
      case 'contact':
        return <Phone className="h-4 w-4" />;
      case 'shipment':
        return <Package className="h-4 w-4" />;
      default:
        return <Activity className="h-4 w-4" />;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Link to="/customers">
            <Button variant="outline" size="sm">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Customers
            </Button>
          </Link>
          <div>
            <h2 className="text-3xl font-bold tracking-tight">{customer.name}</h2>
            <p className="text-muted-foreground">
              Customer since {formatDate(customer.createdAt)}
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Badge className={getStatusColor(customer.status)}>
            {customer.status}
          </Badge>
          <Button>
            <Edit className="mr-2 h-4 w-4" />
            Edit Customer
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(customer.totalRevenue)}</div>
            <p className="text-xs text-muted-foreground">
              +12% from last quarter
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Outstanding Balance</CardTitle>
            <AlertTriangle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(customer.outstandingBalance)}</div>
            <p className="text-xs text-muted-foreground">
              {customer.outstandingBalance > 0 ? 'Requires attention' : 'All current'}
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Credit Utilization</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {Math.round((customer.outstandingBalance / customer.creditLimit) * 100)}%
            </div>
            <p className="text-xs text-muted-foreground">
              {formatCurrency(customer.outstandingBalance)} of {formatCurrency(customer.creditLimit)}
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Payment Days</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{customer.averagePaymentDays}</div>
            <p className="text-xs text-muted-foreground">
              {customer.averagePaymentDays <= 30 ? 'On time' : 'Often late'}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Customer Information & Recent Activity */}
      <div className="grid gap-6 lg:grid-cols-3">
        {/* Customer Information */}
        <div className="lg:col-span-2 space-y-6">
          {/* Contact Information */}
          <Card>
            <CardHeader>
              <CardTitle>Contact Information</CardTitle>
              <CardDescription>
                Primary contact details and business information
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-3">
                  <div className="flex items-center space-x-3">
                    <Mail className="h-4 w-4 text-muted-foreground" />
                    <div>
                      <p className="text-sm font-medium">Email</p>
                      <p className="text-sm text-muted-foreground">{customer.email}</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    <Phone className="h-4 w-4 text-muted-foreground" />
                    <div>
                      <p className="text-sm font-medium">Phone</p>
                      <p className="text-sm text-muted-foreground">{customer.phone}</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    <MapPin className="h-4 w-4 text-muted-foreground" />
                    <div>
                      <p className="text-sm font-medium">Address</p>
                      <p className="text-sm text-muted-foreground">
                        {customer.address}<br />
                        {customer.city}, {customer.state} {customer.zipCode}
                      </p>
                    </div>
                  </div>
                </div>
                <div className="space-y-3">
                  <div>
                    <p className="text-sm font-medium">Industry</p>
                    <p className="text-sm text-muted-foreground">{customer.industry}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium">Website</p>
                    <p className="text-sm text-muted-foreground">{customer.website}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium">Tax ID</p>
                    <p className="text-sm text-muted-foreground">{customer.taxId}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium">Account Manager</p>
                    <p className="text-sm text-muted-foreground">{customer.accountManager}</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Recent Invoices */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>Recent Invoices</CardTitle>
                  <CardDescription>
                    Latest invoices for this customer
                  </CardDescription>
                </div>
                <Button variant="outline" size="sm">
                  <FileText className="mr-2 h-4 w-4" />
                  View All
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {invoices.map((invoice) => (
                  <div key={invoice.id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center space-x-4">
                      <FileText className="h-8 w-8 text-muted-foreground" />
                      <div>
                        <p className="font-medium">{invoice.number}</p>
                        <p className="text-sm text-muted-foreground">
                          Due: {formatDate(invoice.dueDate)}
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-medium">{formatCurrency(invoice.amount)}</p>
                      <Badge className={getStatusColor(invoice.status)}>
                        {invoice.status}
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Recent Activity Sidebar */}
        <div>
          <Card>
            <CardHeader>
              <CardTitle>Recent Activity</CardTitle>
              <CardDescription>
                Latest customer interactions and transactions
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {activities.map((activity) => (
                  <div key={activity.id} className="flex items-start space-x-3">
                    <div className="flex-shrink-0 mt-1">
                      {getActivityIcon(activity.type)}
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium">{activity.description}</p>
                      <div className="flex items-center justify-between mt-1">
                        <p className="text-xs text-muted-foreground">
                          {formatDate(activity.date)}
                        </p>
                        {activity.amount && (
                          <p className="text-xs font-medium">
                            {formatCurrency(activity.amount)}
                          </p>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Payment History */}
          <Card className="mt-6">
            <CardHeader>
              <CardTitle>Recent Payments</CardTitle>
              <CardDescription>
                Latest payment transactions
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {payments.map((payment) => (
                  <div key={payment.id} className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium">{formatCurrency(payment.amount)}</p>
                      <p className="text-xs text-muted-foreground">
                        {payment.method} • {formatDate(payment.date)}
                      </p>
                    </div>
                    <Badge variant="outline">
                      {payment.reference}
                    </Badge>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};