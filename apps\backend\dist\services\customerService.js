"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CustomerService = void 0;
const client_1 = require("@prisma/client");
const shared_utils_1 = require("@ar-scia/shared-utils");
const prisma = new client_1.PrismaClient();
class CustomerService {
    async getCustomers(params) {
        const { page, limit, sortBy, sortOrder, search } = (0, shared_utils_1.transformPaginationParams)(params);
        const { priority, isActive, hasOverdueInvoices, hasDelayedShipments } = params;
        const where = {};
        if (search) {
            where.OR = [
                { name: { contains: search, mode: 'insensitive' } },
                { email: { contains: search, mode: 'insensitive' } },
                { phone: { contains: search, mode: 'insensitive' } }
            ];
        }
        if (priority && priority.length > 0) {
            where.priority = { in: priority };
        }
        if (typeof isActive === 'boolean') {
            where.isActive = isActive;
        }
        if (hasOverdueInvoices) {
            where.invoices = {
                some: {
                    status: 'OVERDUE'
                }
            };
        }
        if (hasDelayedShipments) {
            where.shipments = {
                some: {
                    status: 'DELAYED'
                }
            };
        }
        const total = await prisma.customer.count({ where });
        const customers = await prisma.customer.findMany({
            where,
            include: {
                invoices: {
                    select: {
                        id: true,
                        status: true,
                        amount: true,
                        dueDate: true
                    }
                },
                shipments: {
                    select: {
                        id: true,
                        status: true,
                        estimatedDate: true
                    }
                },
                _count: {
                    select: {
                        invoices: true,
                        shipments: true,
                        communications: true
                    }
                }
            },
            orderBy: { [sortBy]: sortOrder },
            skip: (page - 1) * limit,
            take: limit
        });
        const customersWithMetrics = customers.map(customer => {
            const overdueInvoices = customer.invoices.filter(inv => inv.status === 'OVERDUE');
            const totalOutstanding = customer.invoices
                .filter(inv => inv.status !== 'PAID')
                .reduce((sum, inv) => sum + Number(inv.amount), 0);
            return {
                ...customer,
                metrics: {
                    totalInvoices: customer._count.invoices,
                    totalShipments: customer._count.shipments,
                    totalCommunications: customer._count.communications,
                    overdueInvoices: overdueInvoices.length,
                    totalOutstanding,
                    hasOverdueInvoices: overdueInvoices.length > 0,
                    hasDelayedShipments: customer.shipments.some(s => s.status === 'DELAYED')
                }
            };
        });
        const pagination = (0, shared_utils_1.createPaginationInfo)(page, limit, total);
        return {
            customers: customersWithMetrics,
            pagination
        };
    }
}
exports.CustomerService = CustomerService;
//# sourceMappingURL=customerService.js.map