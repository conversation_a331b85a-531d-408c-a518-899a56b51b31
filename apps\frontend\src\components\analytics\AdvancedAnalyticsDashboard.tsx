import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { DatePickerWithRange } from '@/components/ui/date-range-picker';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  TrendingUp, TrendingDown, BarChart3, PieChart, LineChart, 
  Brain, Target, AlertTriangle, CheckCircle, Clock, 
  Download, RefreshCw, Filter, Zap, Activity, 
  DollarSign, Package, Users, Shield, Calendar
} from 'lucide-react';
import { 
  <PERSON><PERSON><PERSON> as RechartsLine<PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>sponsive<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON> as RechartsBar<PERSON><PERSON>, <PERSON>, <PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>,
  ComposedChart, Area, AreaChart, ScatterChart, Scatter
} from 'recharts';
import { DateRange } from 'react-day-picker';

interface KPIMetric {
  id: string;
  name: string;
  value: number;
  unit: string;
  trend: 'up' | 'down' | 'stable';
  trendPercentage: number;
  target?: number;
  status: 'good' | 'warning' | 'critical';
  description: string;
  domain: string;
}

interface AIInsight {
  id: string;
  type: 'opportunity' | 'risk' | 'trend' | 'anomaly' | 'recommendation';
  title: string;
  description: string;
  confidence: number;
  impact: 'high' | 'medium' | 'low';
  urgency: 'immediate' | 'short_term' | 'long_term';
  affectedDomains: string[];
  recommendedActions: string[];
}

interface TrendData {
  period: string;
  value: number;
  prediction?: number;
  confidence?: number;
}

interface CorrelationData {
  metric1: string;
  metric2: string;
  correlation: number;
  strength: 'strong' | 'moderate' | 'weak';
  direction: 'positive' | 'negative';
}

export const AdvancedAnalyticsDashboard: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [dateRange, setDateRange] = useState<DateRange | undefined>({
    from: new Date(2024, 0, 1),
    to: new Date()
  });
  const [selectedDomains, setSelectedDomains] = useState<string[]>(['ar', 'supply_chain']);
  const [activeTab, setActiveTab] = useState('overview');
  const [kpis, setKpis] = useState<KPIMetric[]>([]);
  const [aiInsights, setAiInsights] = useState<AIInsight[]>([]);
  const [trendData, setTrendData] = useState<Record<string, TrendData[]>>({});
  const [correlations, setCorrelations] = useState<CorrelationData[]>([]);

  useEffect(() => {
    loadAnalyticsData();
  }, [dateRange, selectedDomains]);

  const loadAnalyticsData = async () => {
    setLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // Mock KPI data
      const mockKPIs: KPIMetric[] = [
        {
          id: 'ar-collection-rate',
          name: 'Collection Rate',
          value: 87.3,
          unit: '%',
          trend: 'up',
          trendPercentage: 2.1,
          target: 90,
          status: 'warning',
          description: 'Percentage of receivables collected within terms',
          domain: 'ar'
        },
        {
          id: 'ar-dso',
          name: 'Days Sales Outstanding',
          value: 35.2,
          unit: 'days',
          trend: 'down',
          trendPercentage: -1.8,
          target: 30,
          status: 'warning',
          description: 'Average days to collect receivables',
          domain: 'ar'
        },
        {
          id: 'sc-otd',
          name: 'On-Time Delivery',
          value: 92.1,
          unit: '%',
          trend: 'up',
          trendPercentage: 1.5,
          target: 95,
          status: 'good',
          description: 'Percentage of shipments delivered on time',
          domain: 'supply_chain'
        },
        {
          id: 'sc-cost-efficiency',
          name: 'Cost Efficiency',
          value: 78.5,
          unit: '%',
          trend: 'stable',
          trendPercentage: 0.3,
          target: 80,
          status: 'warning',
          description: 'Supply chain cost optimization index',
          domain: 'supply_chain'
        },
        {
          id: 'customer-satisfaction',
          name: 'Customer Satisfaction',
          value: 4.2,
          unit: '/5',
          trend: 'up',
          trendPercentage: 0.8,
          target: 4.5,
          status: 'good',
          description: 'Average customer satisfaction score',
          domain: 'customer'
        },
        {
          id: 'revenue-growth',
          name: 'Revenue Growth',
          value: 12.8,
          unit: '%',
          trend: 'up',
          trendPercentage: 3.2,
          target: 15,
          status: 'good',
          description: 'Year-over-year revenue growth rate',
          domain: 'financial'
        }
      ];

      // Mock AI insights
      const mockInsights: AIInsight[] = [
        {
          id: 'insight-1',
          type: 'opportunity',
          title: 'Collection Process Optimization Opportunity',
          description: 'AI analysis suggests implementing automated follow-up sequences could improve collection rates by 3-5%',
          confidence: 87,
          impact: 'high',
          urgency: 'short_term',
          affectedDomains: ['ar', 'customer'],
          recommendedActions: [
            'Implement automated payment reminders',
            'Segment customers by payment behavior',
            'Optimize collection timing based on customer patterns'
          ]
        },
        {
          id: 'insight-2',
          type: 'risk',
          title: 'Supply Chain Cost Pressure',
          description: 'Rising transportation costs and seasonal demand patterns indicate potential margin pressure in Q2',
          confidence: 92,
          impact: 'medium',
          urgency: 'immediate',
          affectedDomains: ['supply_chain', 'financial'],
          recommendedActions: [
            'Negotiate volume discounts with carriers',
            'Optimize route planning algorithms',
            'Consider alternative transportation modes'
          ]
        },
        {
          id: 'insight-3',
          type: 'trend',
          title: 'Customer Satisfaction Correlation',
          description: 'Strong correlation detected between on-time delivery and customer satisfaction scores',
          confidence: 94,
          impact: 'high',
          urgency: 'long_term',
          affectedDomains: ['supply_chain', 'customer'],
          recommendedActions: [
            'Prioritize delivery reliability initiatives',
            'Implement proactive communication for delays',
            'Invest in predictive delivery analytics'
          ]
        }
      ];

      // Mock trend data
      const mockTrendData = {
        'Collection Rate': [
          { period: 'Jan', value: 85.2, prediction: 87.8 },
          { period: 'Feb', value: 86.1, prediction: 88.2 },
          { period: 'Mar', value: 84.8, prediction: 87.5 },
          { period: 'Apr', value: 87.3, prediction: 89.1 },
          { period: 'May', value: 88.1, prediction: 89.8 },
          { period: 'Jun', value: 86.9, prediction: 88.9 }
        ],
        'On-Time Delivery': [
          { period: 'Jan', value: 89.5, prediction: 92.8 },
          { period: 'Feb', value: 91.2, prediction: 93.2 },
          { period: 'Mar', value: 90.8, prediction: 92.9 },
          { period: 'Apr', value: 92.1, prediction: 94.1 },
          { period: 'May', value: 91.8, prediction: 93.8 },
          { period: 'Jun', value: 90.3, prediction: 93.3 }
        ]
      };

      // Mock correlation data
      const mockCorrelations: CorrelationData[] = [
        {
          metric1: 'On-Time Delivery',
          metric2: 'Customer Satisfaction',
          correlation: 0.78,
          strength: 'strong',
          direction: 'positive'
        },
        {
          metric1: 'Collection Rate',
          metric2: 'Customer Retention',
          correlation: -0.42,
          strength: 'moderate',
          direction: 'negative'
        },
        {
          metric1: 'Days Sales Outstanding',
          metric2: 'Shipment Delays',
          correlation: 0.65,
          strength: 'strong',
          direction: 'positive'
        }
      ];

      setKpis(mockKPIs);
      setAiInsights(mockInsights);
      setTrendData(mockTrendData);
      setCorrelations(mockCorrelations);
    } catch (error) {
      console.error('Failed to load analytics data:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'good': return 'text-green-600 bg-green-50 border-green-200';
      case 'warning': return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'critical': return 'text-red-600 bg-red-50 border-red-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getImpactColor = (impact: string) => {
    switch (impact) {
      case 'high': return 'text-red-600 bg-red-50';
      case 'medium': return 'text-yellow-600 bg-yellow-50';
      case 'low': return 'text-green-600 bg-green-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up': return <TrendingUp className="w-4 h-4 text-green-600" />;
      case 'down': return <TrendingDown className="w-4 h-4 text-red-600" />;
      default: return <Activity className="w-4 h-4 text-gray-600" />;
    }
  };

  const getInsightIcon = (type: string) => {
    switch (type) {
      case 'opportunity': return <Target className="w-5 h-5 text-blue-600" />;
      case 'risk': return <AlertTriangle className="w-5 h-5 text-red-600" />;
      case 'trend': return <TrendingUp className="w-5 h-5 text-green-600" />;
      case 'anomaly': return <Zap className="w-5 h-5 text-yellow-600" />;
      default: return <Brain className="w-5 h-5 text-purple-600" />;
    }
  };

  const formatValue = (value: number, unit: string) => {
    if (unit === '$') {
      return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
      }).format(value);
    }
    return `${value}${unit}`;
  };

  const generateExecutiveReport = async () => {
    setLoading(true);
    try {
      // Simulate report generation
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // In a real implementation, this would call the backend API
      const reportData = {
        title: 'Executive Business Intelligence Report',
        generatedDate: new Date(),
        kpis: kpis.length,
        insights: aiInsights.length,
        recommendations: aiInsights.reduce((acc, insight) => acc + insight.recommendedActions.length, 0)
      };

      // Simulate download
      const blob = new Blob([JSON.stringify(reportData, null, 2)], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `executive-report-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Failed to generate report:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading && kpis.length === 0) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="mt-2 text-muted-foreground">Loading advanced analytics...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header and Controls */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold">Advanced Analytics</h1>
          <p className="text-muted-foreground">AI-powered business intelligence across all domains</p>
        </div>
        <div className="flex items-center gap-4">
          <DatePickerWithRange
            date={dateRange}
            onDateChange={setDateRange}
          />
          <Select value={selectedDomains.join(',')} onValueChange={(value) => setSelectedDomains(value.split(','))}>
            <SelectTrigger className="w-48">
              <SelectValue placeholder="Select domains" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="ar,supply_chain">AR + Supply Chain</SelectItem>
              <SelectItem value="ar,supply_chain,customer">All Domains</SelectItem>
              <SelectItem value="ar">AR Only</SelectItem>
              <SelectItem value="supply_chain">Supply Chain Only</SelectItem>
            </SelectContent>
          </Select>
          <Button onClick={loadAnalyticsData} variant="outline" disabled={loading}>
            <RefreshCw className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button onClick={generateExecutiveReport} disabled={loading}>
            <Download className="w-4 h-4 mr-2" />
            Executive Report
          </Button>
        </div>
      </div>

      {/* Key Metrics Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4">
        {kpis.slice(0, 6).map((kpi) => (
          <Card key={kpi.id}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">{kpi.name}</CardTitle>
              {getTrendIcon(kpi.trend)}
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatValue(kpi.value, kpi.unit)}</div>
              <div className="flex items-center justify-between mt-2">
                <Badge className={getStatusColor(kpi.status)}>
                  {kpi.status}
                </Badge>
                <span className={`text-xs ${kpi.trend === 'up' ? 'text-green-600' : kpi.trend === 'down' ? 'text-red-600' : 'text-gray-600'}`}>
                  {kpi.trendPercentage > 0 ? '+' : ''}{kpi.trendPercentage}%
                </span>
              </div>
              {kpi.target && (
                <div className="mt-2">
                  <Progress value={(kpi.value / kpi.target) * 100} className="h-1" />
                  <p className="text-xs text-muted-foreground mt-1">
                    Target: {formatValue(kpi.target, kpi.unit)}
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        ))}
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="trends">Trends</TabsTrigger>
          <TabsTrigger value="correlations">Correlations</TabsTrigger>
          <TabsTrigger value="ai-insights">AI Insights</TabsTrigger>
          <TabsTrigger value="forecasting">Forecasting</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {/* AI Insights Summary */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Brain className="w-5 h-5 text-purple-600" />
                AI-Powered Insights Summary
              </CardTitle>
              <CardDescription>
                Latest intelligent analysis across all business domains
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="text-center p-4 border rounded-lg">
                  <Target className="w-8 h-8 mx-auto mb-2 text-blue-600" />
                  <div className="text-2xl font-bold">{aiInsights.filter(i => i.type === 'opportunity').length}</div>
                  <div className="text-sm text-muted-foreground">Opportunities</div>
                </div>
                <div className="text-center p-4 border rounded-lg">
                  <AlertTriangle className="w-8 h-8 mx-auto mb-2 text-red-600" />
                  <div className="text-2xl font-bold">{aiInsights.filter(i => i.type === 'risk').length}</div>
                  <div className="text-sm text-muted-foreground">Risk Factors</div>
                </div>
                <div className="text-center p-4 border rounded-lg">
                  <TrendingUp className="w-8 h-8 mx-auto mb-2 text-green-600" />
                  <div className="text-2xl font-bold">{aiInsights.filter(i => i.type === 'trend').length}</div>
                  <div className="text-sm text-muted-foreground">Trend Insights</div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Performance Heatmap */}
          <Card>
            <CardHeader>
              <CardTitle>Performance Heatmap</CardTitle>
              <CardDescription>Visual overview of KPI performance across domains</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                {kpis.map((kpi) => (
                  <div key={kpi.id} className="text-center p-4 border rounded-lg">
                    <div className={`text-lg font-bold ${
                      kpi.status === 'good' ? 'text-green-600' :
                      kpi.status === 'warning' ? 'text-yellow-600' :
                      'text-red-600'
                    }`}>
                      {formatValue(kpi.value, kpi.unit)}
                    </div>
                    <div className="text-sm text-muted-foreground">{kpi.name}</div>
                    <Badge className={`mt-1 ${getStatusColor(kpi.status)}`}>
                      {kpi.domain}
                    </Badge>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="trends" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {Object.entries(trendData).map(([metric, data]) => (
              <Card key={metric}>
                <CardHeader>
                  <CardTitle>{metric} Trend Analysis</CardTitle>
                  <CardDescription>Historical data with AI predictions</CardDescription>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <ComposedChart data={data}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="period" />
                      <YAxis />
                      <Tooltip />
                      <Bar dataKey="value" fill="#3b82f6" name="Actual" />
                      <Line type="monotone" dataKey="prediction" stroke="#22c55e" strokeWidth={2} strokeDasharray="5 5" name="AI Prediction" />
                    </ComposedChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="correlations" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Cross-Domain Correlations</CardTitle>
              <CardDescription>Statistical relationships between key metrics</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {correlations.map((corr, index) => (
                  <div key={index} className="p-4 border rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <div className="font-medium">
                        {corr.metric1} vs {corr.metric2}
                      </div>
                      <Badge className={
                        corr.strength === 'strong' ? 'bg-green-100 text-green-800' :
                        corr.strength === 'moderate' ? 'bg-yellow-100 text-yellow-800' :
                        'bg-gray-100 text-gray-800'
                      }>
                        {corr.strength} {corr.direction}
                      </Badge>
                    </div>
                    <div className="flex items-center gap-4">
                      <div className="flex-1">
                        <Progress
                          value={Math.abs(corr.correlation) * 100}
                          className="h-2"
                        />
                      </div>
                      <div className="text-sm font-medium">
                        {corr.correlation > 0 ? '+' : ''}{(corr.correlation * 100).toFixed(1)}%
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="ai-insights" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {aiInsights.map((insight) => (
              <Card key={insight.id}>
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <div className="flex items-center gap-2">
                      {getInsightIcon(insight.type)}
                      <CardTitle className="text-lg">{insight.title}</CardTitle>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge className={getImpactColor(insight.impact)}>
                        {insight.impact} impact
                      </Badge>
                      <Badge variant="outline">
                        {insight.confidence}% confidence
                      </Badge>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  <p className="text-muted-foreground">{insight.description}</p>

                  <div>
                    <h4 className="font-medium mb-2">Affected Domains</h4>
                    <div className="flex gap-2">
                      {insight.affectedDomains.map(domain => (
                        <Badge key={domain} variant="secondary">
                          {domain.replace('_', ' ')}
                        </Badge>
                      ))}
                    </div>
                  </div>

                  <div>
                    <h4 className="font-medium mb-2">Recommended Actions</h4>
                    <ul className="space-y-1">
                      {insight.recommendedActions.map((action, index) => (
                        <li key={index} className="text-sm flex items-start gap-2">
                          <CheckCircle className="w-3 h-3 text-green-600 mt-0.5 flex-shrink-0" />
                          {action}
                        </li>
                      ))}
                    </ul>
                  </div>

                  <div className="flex items-center gap-2 text-xs text-muted-foreground">
                    <Clock className="w-3 h-3" />
                    Urgency: {insight.urgency.replace('_', ' ')}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="forecasting" className="space-y-6">
          <Alert>
            <Brain className="h-4 w-4" />
            <AlertDescription>
              AI-powered forecasting uses machine learning models to predict future performance based on historical data and cross-domain correlations.
            </AlertDescription>
          </Alert>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Revenue Forecast</CardTitle>
                <CardDescription>6-month revenue prediction with confidence intervals</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <AreaChart data={[
                    { month: 'Mar', actual: 2800000, forecast: 2850000, upper: 3100000, lower: 2600000 },
                    { month: 'Apr', forecast: 2920000, upper: 3200000, lower: 2640000 },
                    { month: 'May', forecast: 3050000, upper: 3350000, lower: 2750000 },
                    { month: 'Jun', forecast: 3180000, upper: 3500000, lower: 2860000 },
                    { month: 'Jul', forecast: 3250000, upper: 3600000, lower: 2900000 },
                    { month: 'Aug', forecast: 3320000, upper: 3700000, lower: 2940000 }
                  ]}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis />
                    <Tooltip formatter={(value) => [`$${(value as number / 1000000).toFixed(1)}M`, '']} />
                    <Area type="monotone" dataKey="upper" stackId="1" stroke="none" fill="#3b82f6" fillOpacity={0.1} />
                    <Area type="monotone" dataKey="lower" stackId="1" stroke="none" fill="#ffffff" />
                    <Line type="monotone" dataKey="forecast" stroke="#3b82f6" strokeWidth={2} />
                    <Line type="monotone" dataKey="actual" stroke="#22c55e" strokeWidth={2} />
                  </AreaChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Risk Probability Matrix</CardTitle>
                <CardDescription>Predicted risk levels across business domains</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {[
                    { domain: 'Accounts Receivable', risk: 25, trend: 'stable' },
                    { domain: 'Supply Chain', risk: 35, trend: 'increasing' },
                    { domain: 'Customer Relations', risk: 15, trend: 'decreasing' },
                    { domain: 'Financial', risk: 20, trend: 'stable' }
                  ].map((item, index) => (
                    <div key={index} className="flex items-center justify-between p-3 border rounded">
                      <div>
                        <div className="font-medium">{item.domain}</div>
                        <div className="text-sm text-muted-foreground">
                          Trend: {item.trend}
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Progress value={item.risk} className="w-20" />
                        <span className={`text-sm font-medium ${
                          item.risk < 20 ? 'text-green-600' :
                          item.risk < 40 ? 'text-yellow-600' :
                          'text-red-600'
                        }`}>
                          {item.risk}%
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};
