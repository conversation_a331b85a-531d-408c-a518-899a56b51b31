import { GoogleGenerativeAI } from '@google/generative-ai';
import { AppError } from '../utils/errors';

interface CrossDomainContext {
  customer: {
    id: string;
    name: string;
    priority: 'low' | 'medium' | 'high' | 'critical';
    paymentHistory: PaymentHistoryData;
    shipmentHistory: ShipmentHistoryData;
    riskScore: number;
    totalOutstanding: number;
    averagePaymentDays: number;
    relationshipValue: number;
  };
  arData: {
    outstandingInvoices: ARInvoice[];
    overdueAmount: number;
    daysPastDue: number;
    paymentCommitments: PaymentCommitment[];
    recentInteractions: ARInteraction[];
  };
  supplyChainData: {
    activeShipments: Shipment[];
    pendingOrders: Order[];
    exceptions: SupplyChainException[];
    performanceMetrics: PerformanceMetrics;
  };
  businessContext: {
    seasonality: string;
    marketConditions: string;
    companyPriorities: string[];
    riskTolerance: 'low' | 'medium' | 'high';
  };
}

interface PaymentHistoryData {
  averageDaysToPayment: number;
  paymentReliability: number;
  totalPaidAmount: number;
  disputeHistory: number;
}

interface ShipmentHistoryData {
  totalShipments: number;
  onTimeDeliveryRate: number;
  averageShipmentValue: number;
  exceptionRate: number;
}

interface ARInvoice {
  id: string;
  amount: number;
  dueDate: Date;
  daysPastDue: number;
  status: string;
}

interface PaymentCommitment {
  id: string;
  amount: number;
  commitmentDate: Date;
  status: 'pending' | 'fulfilled' | 'broken';
}

interface ARInteraction {
  id: string;
  type: 'email' | 'call' | 'meeting';
  date: Date;
  outcome: string;
  sentiment: 'positive' | 'neutral' | 'negative';
}

interface Shipment {
  id: string;
  value: number;
  status: string;
  estimatedDelivery: Date;
  priority: string;
}

interface Order {
  id: string;
  value: number;
  requestedDelivery: Date;
  priority: string;
}

interface SupplyChainException {
  id: string;
  type: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  impact: string;
  shipmentId?: string;
}

interface PerformanceMetrics {
  onTimeDelivery: number;
  averageTransitTime: number;
  exceptionRate: number;
  customerSatisfaction: number;
}

interface CrossDomainRule {
  id: string;
  name: string;
  description: string;
  priority: number;
  enabled: boolean;
  conditions: CrossDomainCondition[];
  actions: CrossDomainAction[];
  aiEnhanced: boolean;
  domains: ('ar' | 'supply_chain' | 'customer')[];
}

interface CrossDomainCondition {
  domain: 'ar' | 'supply_chain' | 'customer';
  field: string;
  operator: string;
  value: any;
  weight: number;
}

interface CrossDomainAction {
  type: 'escalate_ar' | 'expedite_shipment' | 'hold_shipment' | 'adjust_credit_terms' | 'notify_stakeholders' | 'create_task' | 'schedule_review';
  domain: 'ar' | 'supply_chain' | 'customer';
  parameters: Record<string, any>;
  priority: number;
}

interface RuleEvaluationResult {
  ruleId: string;
  triggered: boolean;
  confidence: number;
  reasoning: string;
  recommendedActions: CrossDomainAction[];
  aiInsights: string;
  riskAssessment: {
    level: 'low' | 'medium' | 'high' | 'critical';
    factors: string[];
    mitigation: string[];
  };
}

interface CrossDomainDecision {
  customerId: string;
  timestamp: Date;
  context: CrossDomainContext;
  triggeredRules: RuleEvaluationResult[];
  finalRecommendations: CrossDomainAction[];
  aiAnalysis: string;
  confidenceScore: number;
  executedActions: string[];
}

class CrossDomainRulesService {
  private genAI: GoogleGenerativeAI;
  private model: any;

  constructor() {
    if (!process.env.GOOGLE_AI_API_KEY) {
      throw new AppError('Google AI API key not configured', 500);
    }
    this.genAI = new GoogleGenerativeAI(process.env.GOOGLE_AI_API_KEY);
    this.model = this.genAI.getGenerativeModel({ model: 'gemini-pro' });
  }

  /**
   * Evaluate cross-domain business rules for a customer
   */
  async evaluateCustomerRules(context: CrossDomainContext): Promise<CrossDomainDecision> {
    try {
      // Get applicable rules
      const rules = await this.getApplicableRules(context);
      
      // Evaluate each rule
      const ruleResults: RuleEvaluationResult[] = [];
      for (const rule of rules) {
        const result = await this.evaluateRule(rule, context);
        if (result.triggered) {
          ruleResults.push(result);
        }
      }

      // Get AI-powered holistic analysis
      const aiAnalysis = await this.getAIHolisticAnalysis(context, ruleResults);

      // Consolidate recommendations
      const finalRecommendations = this.consolidateRecommendations(ruleResults, aiAnalysis);

      const decision: CrossDomainDecision = {
        customerId: context.customer.id,
        timestamp: new Date(),
        context,
        triggeredRules: ruleResults,
        finalRecommendations,
        aiAnalysis: aiAnalysis.analysis,
        confidenceScore: aiAnalysis.confidence,
        executedActions: []
      };

      return decision;
    } catch (error) {
      console.error('Cross-domain rule evaluation error:', error);
      throw new AppError('Failed to evaluate cross-domain rules', 500);
    }
  }

  /**
   * Get AI-powered holistic analysis considering all domains
   */
  private async getAIHolisticAnalysis(
    context: CrossDomainContext, 
    ruleResults: RuleEvaluationResult[]
  ): Promise<{ analysis: string; confidence: number; recommendations: CrossDomainAction[] }> {
    const prompt = `
    As an expert business analyst, analyze this customer's complete business relationship across AR and supply chain domains:

    CUSTOMER PROFILE:
    - Name: ${context.customer.name}
    - Priority: ${context.customer.priority}
    - Risk Score: ${context.customer.riskScore}/100
    - Outstanding Amount: $${context.customer.totalOutstanding.toLocaleString()}
    - Average Payment Days: ${context.customer.averagePaymentDays}
    - Relationship Value: $${context.customer.relationshipValue.toLocaleString()}

    AR SITUATION:
    - Overdue Amount: $${context.arData.overdueAmount.toLocaleString()}
    - Days Past Due: ${context.arData.daysPastDue}
    - Outstanding Invoices: ${context.arData.outstandingInvoices.length}
    - Payment Commitments: ${context.arData.paymentCommitments.length}

    SUPPLY CHAIN STATUS:
    - Active Shipments: ${context.supplyChainData.activeShipments.length}
    - Pending Orders: ${context.supplyChainData.pendingOrders.length}
    - Active Exceptions: ${context.supplyChainData.exceptions.length}
    - On-Time Delivery: ${context.supplyChainData.performanceMetrics.onTimeDelivery}%

    TRIGGERED RULES:
    ${ruleResults.map(r => `- ${r.ruleId}: ${r.reasoning} (Confidence: ${r.confidence}%)`).join('\n')}

    BUSINESS CONTEXT:
    - Season: ${context.businessContext.seasonality}
    - Market: ${context.businessContext.marketConditions}
    - Risk Tolerance: ${context.businessContext.riskTolerance}

    Provide a comprehensive analysis considering:
    1. Overall customer relationship health
    2. Cross-domain risk factors and opportunities
    3. Strategic recommendations balancing AR collection with supply chain service
    4. Potential impact of actions on customer relationship
    5. Priority and timing of recommended actions

    Format your response as JSON:
    {
      "analysis": "Detailed analysis of the situation",
      "confidence": 85,
      "keyRisks": ["risk1", "risk2"],
      "opportunities": ["opp1", "opp2"],
      "strategicRecommendations": ["rec1", "rec2"],
      "timing": "immediate|short_term|long_term",
      "relationshipImpact": "positive|neutral|negative"
    }
    `;

    try {
      const result = await this.model.generateContent(prompt);
      const response = await result.response;
      const text = response.text();
      
      // Parse JSON response
      const jsonMatch = text.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const analysis = JSON.parse(jsonMatch[0]);
        return {
          analysis: analysis.analysis,
          confidence: analysis.confidence || 75,
          recommendations: this.convertAIRecommendationsToActions(analysis.strategicRecommendations || [])
        };
      }

      return {
        analysis: text,
        confidence: 75,
        recommendations: []
      };
    } catch (error) {
      console.error('AI holistic analysis error:', error);
      return {
        analysis: 'AI analysis unavailable - using rule-based evaluation only',
        confidence: 60,
        recommendations: []
      };
    }
  }

  /**
   * Evaluate a specific cross-domain rule
   */
  private async evaluateRule(rule: CrossDomainRule, context: CrossDomainContext): Promise<RuleEvaluationResult> {
    try {
      // Basic rule condition evaluation
      let conditionsMet = 0;
      let totalWeight = 0;

      for (const condition of rule.conditions) {
        const value = this.extractValueFromContext(context, condition.domain, condition.field);
        const met = this.evaluateCondition(value, condition.operator, condition.value);
        
        if (met) {
          conditionsMet += condition.weight;
        }
        totalWeight += condition.weight;
      }

      const basicConfidence = (conditionsMet / totalWeight) * 100;
      const triggered = basicConfidence >= 70; // Threshold for rule triggering

      let aiInsights = '';
      let enhancedConfidence = basicConfidence;

      // AI enhancement if enabled
      if (rule.aiEnhanced && triggered) {
        const aiResult = await this.getAIRuleEnhancement(rule, context, basicConfidence);
        aiInsights = aiResult.insights;
        enhancedConfidence = aiResult.confidence;
      }

      return {
        ruleId: rule.id,
        triggered,
        confidence: enhancedConfidence,
        reasoning: this.generateRuleReasoning(rule, context, conditionsMet, totalWeight),
        recommendedActions: triggered ? rule.actions : [],
        aiInsights,
        riskAssessment: this.assessRisk(context, rule, triggered)
      };
    } catch (error) {
      console.error(`Rule evaluation error for ${rule.id}:`, error);
      return {
        ruleId: rule.id,
        triggered: false,
        confidence: 0,
        reasoning: 'Rule evaluation failed',
        recommendedActions: [],
        aiInsights: '',
        riskAssessment: { level: 'medium', factors: ['evaluation_error'], mitigation: ['manual_review'] }
      };
    }
  }

  /**
   * Get AI enhancement for rule evaluation
   */
  private async getAIRuleEnhancement(
    rule: CrossDomainRule, 
    context: CrossDomainContext, 
    basicConfidence: number
  ): Promise<{ insights: string; confidence: number }> {
    const prompt = `
    Analyze this business rule application with cross-domain context:

    RULE: ${rule.name}
    Description: ${rule.description}
    Basic Confidence: ${basicConfidence}%

    CUSTOMER CONTEXT:
    ${JSON.stringify(context, null, 2)}

    Consider:
    1. Are there nuanced factors that should adjust the confidence?
    2. What are the potential unintended consequences?
    3. How might this action affect the overall customer relationship?
    4. Are there alternative approaches that might be more effective?

    Provide insights and adjusted confidence (0-100):
    `;

    try {
      const result = await this.model.generateContent(prompt);
      const response = await result.response;
      const text = response.text();

      // Extract confidence if mentioned
      const confidenceMatch = text.match(/confidence[:\s]*(\d+)/i);
      const adjustedConfidence = confidenceMatch ? 
        Math.min(100, Math.max(0, parseInt(confidenceMatch[1]))) : 
        basicConfidence;

      return {
        insights: text,
        confidence: adjustedConfidence
      };
    } catch (error) {
      console.error('AI rule enhancement error:', error);
      return {
        insights: 'AI enhancement unavailable',
        confidence: basicConfidence
      };
    }
  }

  /**
   * Extract value from context based on domain and field
   */
  private extractValueFromContext(context: CrossDomainContext, domain: string, field: string): any {
    const fieldPath = field.split('.');
    let value: any;

    switch (domain) {
      case 'customer':
        value = context.customer;
        break;
      case 'ar':
        value = context.arData;
        break;
      case 'supply_chain':
        value = context.supplyChainData;
        break;
      default:
        return null;
    }

    // Navigate nested fields
    for (const part of fieldPath) {
      if (value && typeof value === 'object') {
        value = value[part];
      } else {
        return null;
      }
    }

    return value;
  }

  /**
   * Evaluate a condition
   */
  private evaluateCondition(value: any, operator: string, expectedValue: any): boolean {
    switch (operator) {
      case 'equals':
        return value === expectedValue;
      case 'not_equals':
        return value !== expectedValue;
      case 'greater_than':
        return Number(value) > Number(expectedValue);
      case 'less_than':
        return Number(value) < Number(expectedValue);
      case 'greater_equal':
        return Number(value) >= Number(expectedValue);
      case 'less_equal':
        return Number(value) <= Number(expectedValue);
      case 'contains':
        return String(value).toLowerCase().includes(String(expectedValue).toLowerCase());
      case 'in':
        return Array.isArray(expectedValue) && expectedValue.includes(value);
      case 'between':
        return Array.isArray(expectedValue) && 
               Number(value) >= Number(expectedValue[0]) && 
               Number(value) <= Number(expectedValue[1]);
      default:
        return false;
    }
  }

  /**
   * Generate reasoning for rule evaluation
   */
  private generateRuleReasoning(
    rule: CrossDomainRule, 
    context: CrossDomainContext, 
    conditionsMet: number, 
    totalWeight: number
  ): string {
    const percentage = Math.round((conditionsMet / totalWeight) * 100);
    return `Rule "${rule.name}" evaluated with ${percentage}% condition match. ` +
           `Customer ${context.customer.name} (Priority: ${context.customer.priority}) ` +
           `has $${context.arData.overdueAmount.toLocaleString()} overdue and ` +
           `${context.supplyChainData.activeShipments.length} active shipments.`;
  }

  /**
   * Assess risk based on context and rule
   */
  private assessRisk(
    context: CrossDomainContext, 
    rule: CrossDomainRule, 
    triggered: boolean
  ): { level: 'low' | 'medium' | 'high' | 'critical'; factors: string[]; mitigation: string[] } {
    const factors: string[] = [];
    const mitigation: string[] = [];
    let level: 'low' | 'medium' | 'high' | 'critical' = 'low';

    // Risk factors
    if (context.customer.riskScore > 70) {
      factors.push('High customer risk score');
      level = 'high';
    }
    
    if (context.arData.overdueAmount > 100000) {
      factors.push('Large overdue amount');
      level = level === 'high' ? 'critical' : 'high';
    }

    if (context.supplyChainData.exceptions.length > 3) {
      factors.push('Multiple supply chain exceptions');
      level = level === 'low' ? 'medium' : level;
    }

    if (context.customer.priority === 'critical') {
      factors.push('Critical customer priority');
      mitigation.push('Executive approval required');
    }

    // Default mitigations
    if (triggered) {
      mitigation.push('Monitor customer response');
      mitigation.push('Document all actions');
    }

    return { level, factors, mitigation };
  }

  /**
   * Convert AI recommendations to actionable items
   */
  private convertAIRecommendationsToActions(recommendations: string[]): CrossDomainAction[] {
    return recommendations.map((rec, index) => ({
      type: 'create_task',
      domain: 'customer',
      parameters: {
        description: rec,
        priority: 'medium',
        assignee: 'relationship_manager'
      },
      priority: index + 1
    }));
  }

  /**
   * Consolidate recommendations from multiple rules and AI analysis
   */
  private consolidateRecommendations(
    ruleResults: RuleEvaluationResult[], 
    aiAnalysis: { recommendations: CrossDomainAction[] }
  ): CrossDomainAction[] {
    const allActions: CrossDomainAction[] = [];
    
    // Add rule-based actions
    ruleResults.forEach(result => {
      allActions.push(...result.recommendedActions);
    });

    // Add AI recommendations
    allActions.push(...aiAnalysis.recommendations);

    // Deduplicate and prioritize
    const uniqueActions = this.deduplicateActions(allActions);
    return uniqueActions.sort((a, b) => a.priority - b.priority);
  }

  /**
   * Remove duplicate actions
   */
  private deduplicateActions(actions: CrossDomainAction[]): CrossDomainAction[] {
    const seen = new Set<string>();
    return actions.filter(action => {
      const key = `${action.type}-${action.domain}-${JSON.stringify(action.parameters)}`;
      if (seen.has(key)) {
        return false;
      }
      seen.add(key);
      return true;
    });
  }

  /**
   * Get applicable rules for the context
   */
  private async getApplicableRules(context: CrossDomainContext): Promise<CrossDomainRule[]> {
    // In a real implementation, this would query the database
    // For now, return mock rules
    return [
      {
        id: 'cross-1',
        name: 'High Value Customer Payment Risk',
        description: 'Escalate when high-value customers have payment issues affecting shipments',
        priority: 1,
        enabled: true,
        conditions: [
          {
            domain: 'customer',
            field: 'relationshipValue',
            operator: 'greater_than',
            value: 500000,
            weight: 30
          },
          {
            domain: 'ar',
            field: 'overdueAmount',
            operator: 'greater_than',
            value: 50000,
            weight: 40
          },
          {
            domain: 'supply_chain',
            field: 'activeShipments.length',
            operator: 'greater_than',
            value: 0,
            weight: 30
          }
        ],
        actions: [
          {
            type: 'notify_stakeholders',
            domain: 'customer',
            parameters: {
              recipients: ['<EMAIL>', '<EMAIL>'],
              urgency: 'high',
              template: 'high_value_customer_risk'
            },
            priority: 1
          },
          {
            type: 'schedule_review',
            domain: 'customer',
            parameters: {
              type: 'executive_review',
              timeframe: '24_hours'
            },
            priority: 2
          }
        ],
        aiEnhanced: true,
        domains: ['ar', 'supply_chain', 'customer']
      }
    ];
  }

  /**
   * Execute cross-domain actions
   */
  async executeActions(decision: CrossDomainDecision): Promise<string[]> {
    const executedActions: string[] = [];

    for (const action of decision.finalRecommendations) {
      try {
        const result = await this.executeAction(action, decision.context);
        executedActions.push(`${action.type}: ${result}`);
      } catch (error) {
        console.error(`Failed to execute action ${action.type}:`, error);
        executedActions.push(`${action.type}: FAILED - ${error.message}`);
      }
    }

    return executedActions;
  }

  /**
   * Execute a specific action
   */
  private async executeAction(action: CrossDomainAction, context: CrossDomainContext): Promise<string> {
    switch (action.type) {
      case 'notify_stakeholders':
        // In real implementation, send notifications
        return `Notified ${action.parameters.recipients?.join(', ')}`;
      
      case 'create_task':
        // In real implementation, create task in system
        return `Created task: ${action.parameters.description}`;
      
      case 'schedule_review':
        // In real implementation, schedule review
        return `Scheduled ${action.parameters.type} review`;
      
      case 'escalate_ar':
        // In real implementation, escalate AR case
        return `Escalated AR case for customer ${context.customer.name}`;
      
      case 'expedite_shipment':
        // In real implementation, expedite shipments
        return `Expedited shipments for customer ${context.customer.name}`;
      
      case 'hold_shipment':
        // In real implementation, hold shipments
        return `Held shipments for customer ${context.customer.name}`;
      
      default:
        return `Executed ${action.type}`;
    }
  }
}

export const crossDomainRulesService = new CrossDomainRulesService();
