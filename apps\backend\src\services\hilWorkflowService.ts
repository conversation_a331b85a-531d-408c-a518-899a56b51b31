import { GoogleGenerativeAI } from '@google/generative-ai';
import { logger } from '../utils/logger';

interface HILTask {
  id: string;
  type: 'approval' | 'review' | 'decision' | 'exception_handling' | 'validation';
  title: string;
  description: string;
  priority: 'critical' | 'high' | 'medium' | 'low';
  urgency: 'immediate' | 'today' | 'this_week' | 'next_week';
  domain: 'ar' | 'supply_chain' | 'customer' | 'cross_domain';
  context: {
    customerId?: string;
    invoiceId?: string;
    shipmentId?: string;
    amount?: number;
    relatedData: Record<string, any>;
    businessRules: string[];
    riskFactors: string[];
  };
  aiRecommendation: {
    action: string;
    confidence: number;
    reasoning: string;
    alternatives: string[];
    riskAssessment: string;
  };
  assignedTo?: string;
  status: 'pending' | 'in_progress' | 'completed' | 'escalated' | 'cancelled';
  createdAt: Date;
  updatedAt: Date;
  dueDate: Date;
  escalationPath: string[];
  requiredApprovals: number;
  currentApprovals: number;
}

interface HILWorkflowConfig {
  autoEscalationHours: number;
  priorityWeights: Record<string, number>;
  urgencyMultipliers: Record<string, number>;
  domainSpecialists: Record<string, string[]>;
  approvalThresholds: Record<string, number>;
}

interface TaskPrioritization {
  taskId: string;
  score: number;
  factors: {
    priority: number;
    urgency: number;
    businessImpact: number;
    customerImpact: number;
    aiConfidence: number;
    timeToDeadline: number;
  };
  recommendedAssignee: string;
  estimatedDuration: number;
}

export class HILWorkflowService {
  private genAI: GoogleGenerativeAI;
  private config: HILWorkflowConfig;

  constructor() {
    this.genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY || '');
    this.config = {
      autoEscalationHours: 24,
      priorityWeights: {
        critical: 100,
        high: 75,
        medium: 50,
        low: 25
      },
      urgencyMultipliers: {
        immediate: 4.0,
        today: 2.0,
        this_week: 1.5,
        next_week: 1.0
      },
      domainSpecialists: {
        ar: ['ar_manager', 'collections_specialist', 'credit_analyst'],
        supply_chain: ['logistics_manager', 'operations_specialist', 'warehouse_supervisor'],
        customer: ['customer_success_manager', 'account_manager', 'support_specialist'],
        cross_domain: ['business_analyst', 'operations_director', 'general_manager']
      },
      approvalThresholds: {
        credit_adjustment: 10000,
        payment_plan: 5000,
        shipment_reroute: 15000,
        customer_escalation: 25000
      }
    };
  }

  async createHILTask(
    type: HILTask['type'],
    title: string,
    description: string,
    context: HILTask['context'],
    domain: HILTask['domain']
  ): Promise<HILTask> {
    try {
      // Generate AI recommendation
      const aiRecommendation = await this.generateAIRecommendation(type, context, domain);
      
      // Calculate priority and urgency
      const { priority, urgency } = await this.calculatePriorityAndUrgency(context, domain);
      
      // Calculate due date
      const dueDate = this.calculateDueDate(priority, urgency);
      
      // Determine escalation path
      const escalationPath = this.getEscalationPath(domain, priority);

      const task: HILTask = {
        id: `hil_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        type,
        title,
        description,
        priority,
        urgency,
        domain,
        context,
        aiRecommendation,
        status: 'pending',
        createdAt: new Date(),
        updatedAt: new Date(),
        dueDate,
        escalationPath,
        requiredApprovals: this.getRequiredApprovals(type, context),
        currentApprovals: 0
      };

      logger.info('HIL task created', { taskId: task.id, type, domain, priority });
      return task;
    } catch (error) {
      logger.error('Failed to create HIL task', { error, type, domain });
      throw error;
    }
  }

  async prioritizeTasks(tasks: HILTask[]): Promise<TaskPrioritization[]> {
    try {
      const prioritizations: TaskPrioritization[] = [];

      for (const task of tasks) {
        const score = await this.calculateTaskScore(task);
        const recommendedAssignee = await this.getRecommendedAssignee(task);
        const estimatedDuration = this.estimateTaskDuration(task);

        prioritizations.push({
          taskId: task.id,
          score,
          factors: score as any, // Will be properly typed in implementation
          recommendedAssignee,
          estimatedDuration
        });
      }

      // Sort by score (highest first)
      return prioritizations.sort((a, b) => b.score - a.score);
    } catch (error) {
      logger.error('Failed to prioritize tasks', { error });
      throw error;
    }
  }

  async generateContextualGuidance(task: HILTask): Promise<{
    summary: string;
    keyFactors: string[];
    recommendations: string[];
    risks: string[];
    similarCases: string[];
    nextSteps: string[];
  }> {
    try {
      const model = this.genAI.getGenerativeModel({ model: 'gemini-pro' });
      
      const prompt = `
        Analyze this Human-in-the-Loop task and provide contextual guidance:
        
        Task Type: ${task.type}
        Title: ${task.title}
        Description: ${task.description}
        Domain: ${task.domain}
        Priority: ${task.priority}
        Urgency: ${task.urgency}
        
        Context:
        ${JSON.stringify(task.context, null, 2)}
        
        AI Recommendation:
        ${JSON.stringify(task.aiRecommendation, null, 2)}
        
        Please provide:
        1. A concise summary of the situation
        2. Key factors to consider
        3. Specific recommendations for the human reviewer
        4. Potential risks and mitigation strategies
        5. Similar cases or precedents to consider
        6. Clear next steps for resolution
        
        Format as JSON with the structure:
        {
          "summary": "...",
          "keyFactors": ["...", "..."],
          "recommendations": ["...", "..."],
          "risks": ["...", "..."],
          "similarCases": ["...", "..."],
          "nextSteps": ["...", "..."]
        }
      `;

      const result = await model.generateContent(prompt);
      const response = result.response.text();
      
      // Parse JSON response
      const guidance = JSON.parse(response.replace(/```json\n?|\n?```/g, ''));
      
      logger.info('Generated contextual guidance', { taskId: task.id });
      return guidance;
    } catch (error) {
      logger.error('Failed to generate contextual guidance', { error, taskId: task.id });
      
      // Fallback guidance
      return {
        summary: `${task.type} task requiring human review in ${task.domain} domain`,
        keyFactors: ['Review AI recommendation', 'Consider business impact', 'Assess customer relationship'],
        recommendations: ['Follow standard procedures', 'Consult with domain expert if needed'],
        risks: ['Delayed resolution', 'Customer impact'],
        similarCases: ['No similar cases found'],
        nextSteps: ['Review details', 'Make decision', 'Document outcome']
      };
    }
  }

  async updateTaskStatus(
    taskId: string,
    status: HILTask['status'],
    assignedTo?: string,
    notes?: string
  ): Promise<void> {
    try {
      // In a real implementation, this would update the database
      logger.info('HIL task status updated', { taskId, status, assignedTo, notes });
      
      // Check for auto-escalation
      if (status === 'in_progress') {
        await this.scheduleAutoEscalation(taskId);
      }
    } catch (error) {
      logger.error('Failed to update task status', { error, taskId });
      throw error;
    }
  }

  async getTasksForUser(userId: string, filters?: {
    domain?: string;
    priority?: string;
    status?: string;
  }): Promise<HILTask[]> {
    try {
      // In a real implementation, this would query the database
      // For now, return mock data
      const mockTasks: HILTask[] = [
        {
          id: 'hil_001',
          type: 'approval',
          title: 'Credit Limit Increase Request',
          description: 'TechFlow Inc requesting credit limit increase from $100K to $150K',
          priority: 'high',
          urgency: 'today',
          domain: 'ar',
          context: {
            customerId: 'CUST001',
            amount: 50000,
            relatedData: {
              currentCreditLimit: 100000,
              requestedLimit: 150000,
              paymentHistory: 'excellent',
              currentBalance: 25000
            },
            businessRules: ['Credit increase requires manager approval', 'Payment history review required'],
            riskFactors: ['Large increase amount', 'Recent market volatility']
          },
          aiRecommendation: {
            action: 'Approve with conditions',
            confidence: 85,
            reasoning: 'Excellent payment history and strong financial position support approval',
            alternatives: ['Approve partial increase', 'Request additional documentation'],
            riskAssessment: 'Low risk based on payment history and financial stability'
          },
          assignedTo: userId,
          status: 'pending',
          createdAt: new Date('2024-02-10T09:00:00'),
          updatedAt: new Date('2024-02-10T09:00:00'),
          dueDate: new Date('2024-02-10T17:00:00'),
          escalationPath: ['ar_manager', 'credit_director', 'cfo'],
          requiredApprovals: 1,
          currentApprovals: 0
        }
      ];

      return mockTasks.filter(task => {
        if (filters?.domain && task.domain !== filters.domain) return false;
        if (filters?.priority && task.priority !== filters.priority) return false;
        if (filters?.status && task.status !== filters.status) return false;
        return true;
      });
    } catch (error) {
      logger.error('Failed to get tasks for user', { error, userId });
      throw error;
    }
  }

  private async generateAIRecommendation(
    type: HILTask['type'],
    context: HILTask['context'],
    domain: HILTask['domain']
  ): Promise<HILTask['aiRecommendation']> {
    try {
      const model = this.genAI.getGenerativeModel({ model: 'gemini-pro' });
      
      const prompt = `
        Analyze this ${type} task in the ${domain} domain and provide a recommendation:
        
        Context: ${JSON.stringify(context, null, 2)}
        
        Provide a JSON response with:
        - action: recommended action to take
        - confidence: confidence level (0-100)
        - reasoning: explanation of the recommendation
        - alternatives: array of alternative actions
        - riskAssessment: assessment of risks involved
      `;

      const result = await model.generateContent(prompt);
      const response = result.response.text();
      
      return JSON.parse(response.replace(/```json\n?|\n?```/g, ''));
    } catch (error) {
      logger.error('Failed to generate AI recommendation', { error });
      
      // Fallback recommendation
      return {
        action: 'Manual review required',
        confidence: 50,
        reasoning: 'Insufficient data for automated recommendation',
        alternatives: ['Escalate to specialist', 'Request additional information'],
        riskAssessment: 'Medium risk - requires human judgment'
      };
    }
  }

  private async calculatePriorityAndUrgency(
    context: HILTask['context'],
    domain: HILTask['domain']
  ): Promise<{ priority: HILTask['priority']; urgency: HILTask['urgency'] }> {
    // Simplified priority/urgency calculation
    const amount = context.amount || 0;
    const riskFactors = context.riskFactors?.length || 0;

    let priority: HILTask['priority'] = 'medium';
    let urgency: HILTask['urgency'] = 'this_week';

    if (amount > 50000 || riskFactors > 2) {
      priority = 'critical';
      urgency = 'immediate';
    } else if (amount > 25000 || riskFactors > 1) {
      priority = 'high';
      urgency = 'today';
    }

    return { priority, urgency };
  }

  private calculateDueDate(priority: HILTask['priority'], urgency: HILTask['urgency']): Date {
    const now = new Date();
    let hoursToAdd = 168; // Default: 1 week

    if (urgency === 'immediate') hoursToAdd = 4;
    else if (urgency === 'today') hoursToAdd = 24;
    else if (urgency === 'this_week') hoursToAdd = 72;

    return new Date(now.getTime() + hoursToAdd * 60 * 60 * 1000);
  }

  private getEscalationPath(domain: HILTask['domain'], priority: HILTask['priority']): string[] {
    const specialists = this.config.domainSpecialists[domain] || [];
    
    if (priority === 'critical') {
      return [...specialists, 'operations_director', 'general_manager'];
    } else if (priority === 'high') {
      return [...specialists, 'operations_director'];
    }
    
    return specialists;
  }

  private getRequiredApprovals(type: HILTask['type'], context: HILTask['context']): number {
    const amount = context.amount || 0;
    
    if (type === 'approval' && amount > 25000) return 2;
    if (type === 'approval' && amount > 10000) return 1;
    if (type === 'exception_handling') return 1;
    
    return 1;
  }

  private async calculateTaskScore(task: HILTask): Promise<number> {
    const priorityScore = this.config.priorityWeights[task.priority] || 50;
    const urgencyMultiplier = this.config.urgencyMultipliers[task.urgency] || 1.0;
    const timeToDeadline = (task.dueDate.getTime() - Date.now()) / (1000 * 60 * 60); // hours
    const deadlineUrgency = Math.max(0, (48 - timeToDeadline) / 48) * 50; // 0-50 points
    
    return (priorityScore * urgencyMultiplier) + deadlineUrgency;
  }

  private async getRecommendedAssignee(task: HILTask): Promise<string> {
    const specialists = this.config.domainSpecialists[task.domain] || [];
    return specialists[0] || 'general_manager';
  }

  private estimateTaskDuration(task: HILTask): number {
    // Return estimated duration in minutes
    const baseDuration = {
      approval: 15,
      review: 30,
      decision: 45,
      exception_handling: 60,
      validation: 20
    };

    return baseDuration[task.type] || 30;
  }

  private async scheduleAutoEscalation(taskId: string): Promise<void> {
    // In a real implementation, this would schedule a background job
    logger.info('Auto-escalation scheduled', { taskId, hours: this.config.autoEscalationHours });
  }
}
