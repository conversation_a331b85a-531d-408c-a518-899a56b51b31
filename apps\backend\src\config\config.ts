import { z } from 'zod';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const configSchema = z.object({
  nodeEnv: z.enum(['development', 'production', 'test']).default('development'),
  port: z.coerce.number().default(3001),

  // Database
  databaseUrl: z.string().min(1),

  // Redis
  redisUrl: z.string().min(1),

  // JWT
  jwtSecret: z.string().min(32),
  jwtExpiresIn: z.string().default('24h'),

  // CORS
  corsOrigin: z.string().default('http://localhost:3000'),

  // Gemini AI
  geminiApiKey: z.string().min(1),

  // File Upload
  uploadMaxSize: z.coerce.number().default(10 * 1024 * 1024), // 10MB
  uploadPath: z.string().default('./uploads'),

  // Email (for future use)
  emailFrom: z.string().email().optional().or(z.literal('')),
  emailApiKey: z.string().optional(),

  // External APIs
  xeroClientId: z.string().optional(),
  xeroClientSecret: z.string().optional(),
  fishbowlApiUrl: z.string().optional(),
  fishbowlApiKey: z.string().optional(),
});

const env = {
  nodeEnv: process.env.NODE_ENV,
  port: process.env.PORT,
  databaseUrl: process.env.DATABASE_URL,
  redisUrl: process.env.REDIS_URL,
  jwtSecret: process.env.JWT_SECRET,
  jwtExpiresIn: process.env.JWT_EXPIRES_IN,
  corsOrigin: process.env.CORS_ORIGIN,
  geminiApiKey: process.env.GEMINI_API_KEY,
  uploadMaxSize: process.env.UPLOAD_MAX_SIZE,
  uploadPath: process.env.UPLOAD_PATH,
  emailFrom: process.env.EMAIL_FROM,
  emailApiKey: process.env.EMAIL_API_KEY,
  xeroClientId: process.env.XERO_CLIENT_ID,
  xeroClientSecret: process.env.XERO_CLIENT_SECRET,
  fishbowlApiUrl: process.env.FISHBOWL_API_URL,
  fishbowlApiKey: process.env.FISHBOWL_API_KEY,
};

export const config = configSchema.parse(env);

// Validate required environment variables
if (config.nodeEnv === 'production') {
  const requiredVars = [
    'DATABASE_URL',
    'REDIS_URL',
    'JWT_SECRET',
    'GEMINI_API_KEY'
  ];

  const missingVars = requiredVars.filter(varName => !process.env[varName]);

  if (missingVars.length > 0) {
    console.error('❌ Missing required environment variables:', missingVars);
    process.exit(1);
  }
}

console.log('✅ Configuration loaded successfully');
console.log(`📊 Environment: ${config.nodeEnv}`);
console.log(`🔌 Port: ${config.port}`);
console.log(`🔗 CORS Origin: ${config.corsOrigin}`);