import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import { PrismaClient } from '@prisma/client';
import { AppError, extractPermissions } from '@ar-scia/shared-utils';
import { LoginRequest, RegisterRequest, AuthResponse, User } from '@ar-scia/shared-types';
import { config } from '@/config/config';

const prisma = new PrismaClient();

export class AuthService {
  async login(loginData: LoginRequest): Promise<AuthResponse> {
    const { email, password } = loginData;

    // Find user with roles and permissions
    const user = await prisma.user.findUnique({
      where: { email },
      include: {
        roles: {
          include: {
            role: {
              include: {
                permissions: {
                  include: {
                    permission: true
                  }
                }
              }
            }
          }
        }
      }
    });

    if (!user) {
      throw new AppError('Invalid credentials', 401);
    }

    if (!user.isActive) {
      throw new AppError('Account is deactivated', 401);
    }

    // Verify password
    const isPasswordValid = await bcrypt.compare(password, user.password);
    if (!isPasswordValid) {
      throw new AppError('Invalid credentials', 401);
    }

    // Generate JWT token
    const token = jwt.sign(
      {
        userId: user.id,
        email: user.email,
        roles: user.roles.map(ur => ur.role.name)
      },
      config.jwtSecret,
      { expiresIn: config.jwtExpiresIn }
    );

    // Calculate expiration date
    const expiresAt = new Date();
    expiresAt.setTime(expiresAt.getTime() + 24 * 60 * 60 * 1000); // 24 hours

    // Store session
    await prisma.session.create({
      data: {
        userId: user.id,
        token,
        expiresAt
      }
    });

    // Extract permissions
    const permissions = extractPermissions(
      user.roles.map(ur => ({
        permissions: ur.role.permissions.map(rp => rp.permission)
      }))
    );

    // Prepare user response (without password)
    const userResponse: User = {
      id: user.id,
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
      isActive: user.isActive,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
      roles: user.roles.map(ur => ({
        userId: ur.userId,
        roleId: ur.roleId,
        user: {} as User, // Avoid circular reference
        role: {
          id: ur.role.id,
          name: ur.role.name,
          description: ur.role.description,
          permissions: ur.role.permissions.map(rp => rp.permission)
        }
      }))
    };

    return {
      user: userResponse,
      token,
      expiresAt
    };
  }

  async register(registerData: RegisterRequest): Promise<AuthResponse> {
    const { email, password, firstName, lastName } = registerData;

    // Check if user already exists
    const existingUser = await prisma.user.findUnique({
      where: { email }
    });

    if (existingUser) {
      throw new AppError('User already exists with this email', 409);
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 12);

    // Create user with default role
    const user = await prisma.user.create({
      data: {
        email,
        password: hashedPassword,
        firstName,
        lastName,
        roles: {
          create: {
            role: {
              connect: {
                name: 'USER' // Default role
              }
            }
          }
        }
      },
      include: {
        roles: {
          include: {
            role: {
              include: {
                permissions: {
                  include: {
                    permission: true
                  }
                }
              }
            }
          }
        }
      }
    });

    // Generate JWT token
    const token = jwt.sign(
      {
        userId: user.id,
        email: user.email,
        roles: user.roles.map(ur => ur.role.name)
      },
      config.jwtSecret,
      { expiresIn: config.jwtExpiresIn }
    );

    // Calculate expiration date
    const expiresAt = new Date();
    expiresAt.setTime(expiresAt.getTime() + 24 * 60 * 60 * 1000); // 24 hours

    // Store session
    await prisma.session.create({
      data: {
        userId: user.id,
        token,
        expiresAt
      }
    });

    // Extract permissions
    const permissions = extractPermissions(
      user.roles.map(ur => ({
        permissions: ur.role.permissions.map(rp => rp.permission)
      }))
    );

    // Prepare user response (without password)
    const userResponse: User = {
      id: user.id,
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
      isActive: user.isActive,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
      roles: user.roles.map(ur => ({
        userId: ur.userId,
        roleId: ur.roleId,
        user: {} as User, // Avoid circular reference
        role: {
          id: ur.role.id,
          name: ur.role.name,
          description: ur.role.description,
          permissions: ur.role.permissions.map(rp => rp.permission)
        }
      }))
    };

    return {
      user: userResponse,
      token,
      expiresAt
    };
  }

  async logout(token: string): Promise<void> {
    // Remove session from database
    await prisma.session.delete({
      where: { token }
    });
  }

  async validateToken(token: string): Promise<User | null> {
    try {
      // Verify JWT token
      const decoded = jwt.verify(token, config.jwtSecret) as any;

      // Check if session exists and is not expired
      const session = await prisma.session.findUnique({
        where: { token },
        include: {
          user: {
            include: {
              roles: {
                include: {
                  role: {
                    include: {
                      permissions: {
                        include: {
                          permission: true
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      });

      if (!session || session.expiresAt < new Date()) {
        return null;
      }

      if (!session.user.isActive) {
        return null;
      }

      // Return user without password
      const userResponse: User = {
        id: session.user.id,
        email: session.user.email,
        firstName: session.user.firstName,
        lastName: session.user.lastName,
        isActive: session.user.isActive,
        createdAt: session.user.createdAt,
        updatedAt: session.user.updatedAt,
        roles: session.user.roles.map(ur => ({
          userId: ur.userId,
          roleId: ur.roleId,
          user: {} as User, // Avoid circular reference
          role: {
            id: ur.role.id,
            name: ur.role.name,
            description: ur.role.description,
            permissions: ur.role.permissions.map(rp => rp.permission)
          }
        }))
      };

      return userResponse;
    } catch (error) {
      return null;
    }
  }

  async refreshToken(oldToken: string): Promise<AuthResponse> {
    const user = await this.validateToken(oldToken);
    if (!user) {
      throw new AppError('Invalid token', 401);
    }

    // Remove old session
    await this.logout(oldToken);

    // Generate new token
    const token = jwt.sign(
      {
        userId: user.id,
        email: user.email,
        roles: user.roles.map(ur => ur.role.name)
      },
      config.jwtSecret,
      { expiresIn: config.jwtExpiresIn }
    );

    // Calculate expiration date
    const expiresAt = new Date();
    expiresAt.setTime(expiresAt.getTime() + 24 * 60 * 60 * 1000); // 24 hours

    // Store new session
    await prisma.session.create({
      data: {
        userId: user.id,
        token,
        expiresAt
      }
    });

    return {
      user,
      token,
      expiresAt
    };
  }

  async getCurrentUser(userId: string): Promise<User> {
    const user = await prisma.user.findUnique({
      where: {
        id: userId,
        isActive: true
      },
      include: {
        roles: {
          include: {
            role: {
              include: {
                permissions: {
                  include: {
                    permission: true
                  }
                }
              }
            }
          }
        }
      }
    });

    if (!user) {
      throw new AppError('User not found', 404);
    }

    // Return user without password
    const userResponse: User = {
      id: user.id,
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
      isActive: user.isActive,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
      roles: user.roles.map(ur => ({
        userId: ur.userId,
        roleId: ur.roleId,
        user: {} as User, // Avoid circular reference
        role: {
          id: ur.role.id,
          name: ur.role.name,
          description: ur.role.description,
          permissions: ur.role.permissions.map(rp => rp.permission)
        }
      }))
    };

    return userResponse;
  }
}