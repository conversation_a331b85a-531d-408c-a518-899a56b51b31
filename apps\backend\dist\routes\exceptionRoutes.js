"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.exceptionRoutes = void 0;
const express_1 = require("express");
const auth_1 = require("@/middleware/auth");
const shared_utils_1 = require("@ar-scia/shared-utils");
const router = (0, express_1.Router)();
exports.exceptionRoutes = router;
router.use(auth_1.authenticateToken);
router.get('/', (0, auth_1.requirePermission)('exceptions:read'), (req, res) => {
    res.json((0, shared_utils_1.createApiResponse)(true, [], undefined, 'Exceptions retrieved successfully'));
});
//# sourceMappingURL=exceptionRoutes.js.map