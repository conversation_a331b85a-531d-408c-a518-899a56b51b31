"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.optionalAuth = exports.requireAnyRole = exports.requireRole = exports.requirePermission = exports.authenticateToken = void 0;
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const client_1 = require("@prisma/client");
const shared_utils_1 = require("@ar-scia/shared-utils");
const config_1 = require("@/config/config");
const prisma = new client_1.PrismaClient();
const authenticateToken = async (req, res, next) => {
    try {
        const authHeader = req.headers.authorization;
        const token = authHeader && authHeader.split(' ')[1];
        if (!token) {
            throw new shared_utils_1.AppError('Access token required', 401);
        }
        const decoded = jsonwebtoken_1.default.verify(token, config_1.config.jwtSecret);
        const user = await prisma.user.findUnique({
            where: {
                id: decoded.userId,
                isActive: true
            },
            include: {
                roles: {
                    include: {
                        role: {
                            include: {
                                permissions: {
                                    include: {
                                        permission: true
                                    }
                                }
                            }
                        }
                    }
                }
            }
        });
        if (!user) {
            throw new shared_utils_1.AppError('User not found or inactive', 401);
        }
        const permissions = (0, shared_utils_1.extractPermissions)(user.roles.map(ur => ({
            permissions: ur.role.permissions.map(rp => rp.permission)
        })));
        req.user = {
            id: user.id,
            email: user.email,
            firstName: user.firstName,
            lastName: user.lastName,
            roles: user.roles.map(ur => ur.role.name),
            permissions
        };
        next();
    }
    catch (error) {
        if (error instanceof jsonwebtoken_1.default.JsonWebTokenError) {
            return next(new shared_utils_1.AppError('Invalid token', 401));
        }
        if (error instanceof jsonwebtoken_1.default.TokenExpiredError) {
            return next(new shared_utils_1.AppError('Token expired', 401));
        }
        next(error);
    }
};
exports.authenticateToken = authenticateToken;
const requirePermission = (permission) => {
    return (req, res, next) => {
        if (!req.user) {
            return next(new shared_utils_1.AppError('Authentication required', 401));
        }
        if (!(0, shared_utils_1.hasPermission)(req.user.permissions, permission)) {
            return next(new shared_utils_1.AppError('Insufficient permissions', 403));
        }
        next();
    };
};
exports.requirePermission = requirePermission;
const requireRole = (role) => {
    return (req, res, next) => {
        if (!req.user) {
            return next(new shared_utils_1.AppError('Authentication required', 401));
        }
        if (!req.user.roles.includes(role) && !req.user.roles.includes('SUPER_ADMIN')) {
            return next(new shared_utils_1.AppError('Insufficient role permissions', 403));
        }
        next();
    };
};
exports.requireRole = requireRole;
const requireAnyRole = (roles) => {
    return (req, res, next) => {
        if (!req.user) {
            return next(new shared_utils_1.AppError('Authentication required', 401));
        }
        const hasRequiredRole = roles.some(role => req.user.roles.includes(role)) ||
            req.user.roles.includes('SUPER_ADMIN');
        if (!hasRequiredRole) {
            return next(new shared_utils_1.AppError('Insufficient role permissions', 403));
        }
        next();
    };
};
exports.requireAnyRole = requireAnyRole;
const optionalAuth = async (req, res, next) => {
    try {
        const authHeader = req.headers.authorization;
        const token = authHeader && authHeader.split(' ')[1];
        if (token) {
            const decoded = jsonwebtoken_1.default.verify(token, config_1.config.jwtSecret);
            const user = await prisma.user.findUnique({
                where: {
                    id: decoded.userId,
                    isActive: true
                },
                include: {
                    roles: {
                        include: {
                            role: {
                                include: {
                                    permissions: {
                                        include: {
                                            permission: true
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            });
            if (user) {
                const permissions = (0, shared_utils_1.extractPermissions)(user.roles.map(ur => ({
                    permissions: ur.role.permissions.map(rp => rp.permission)
                })));
                req.user = {
                    id: user.id,
                    email: user.email,
                    firstName: user.firstName,
                    lastName: user.lastName,
                    roles: user.roles.map(ur => ur.role.name),
                    permissions
                };
            }
        }
        next();
    }
    catch (error) {
        next();
    }
};
exports.optionalAuth = optionalAuth;
//# sourceMappingURL=auth.js.map