"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.userRoutes = void 0;
const express_1 = require("express");
const auth_1 = require("@/middleware/auth");
const shared_utils_1 = require("@ar-scia/shared-utils");
const router = (0, express_1.Router)();
exports.userRoutes = router;
router.use(auth_1.authenticateToken);
router.get('/', (0, auth_1.requirePermission)('users:read'), (req, res) => {
    res.json((0, shared_utils_1.createApiResponse)(true, [], undefined, 'Users retrieved successfully'));
});
//# sourceMappingURL=userRoutes.js.map