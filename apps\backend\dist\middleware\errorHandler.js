"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.asyncHandler = exports.errorHandler = void 0;
const zod_1 = require("zod");
const shared_utils_1 = require("@ar-scia/shared-utils");
const config_1 = require("@/config/config");
const errorHandler = (error, req, res, next) => {
    let statusCode = 500;
    let message = 'Internal server error';
    let details = undefined;
    if (error instanceof shared_utils_1.AppError) {
        statusCode = error.statusCode;
        message = error.message;
    }
    else if (error instanceof zod_1.ZodError) {
        statusCode = 400;
        message = 'Validation error';
        details = error.errors.map(err => ({
            field: err.path.join('.'),
            message: err.message,
            code: err.code
        }));
    }
    else if (error.name === 'JsonWebTokenError') {
        statusCode = 401;
        message = 'Invalid token';
    }
    else if (error.name === 'TokenExpiredError') {
        statusCode = 401;
        message = 'Token expired';
    }
    else if (error.name === 'PrismaClientKnownRequestError') {
        const prismaError = error;
        switch (prismaError.code) {
            case 'P2002':
                statusCode = 409;
                message = 'Resource already exists';
                details = { field: prismaError.meta?.target };
                break;
            case 'P2025':
                statusCode = 404;
                message = 'Resource not found';
                break;
            case 'P2003':
                statusCode = 400;
                message = 'Foreign key constraint failed';
                break;
            default:
                statusCode = 400;
                message = 'Database error';
        }
    }
    else if (error.name === 'MulterError') {
        statusCode = 400;
        message = 'File upload error';
        if (error.message.includes('File too large')) {
            message = 'File size exceeds limit';
        }
    }
    if (config_1.config.nodeEnv === 'development') {
        console.error('❌ Error:', {
            message: error.message,
            stack: error.stack,
            url: req.url,
            method: req.method,
            body: req.body,
            params: req.params,
            query: req.query,
        });
    }
    const response = (0, shared_utils_1.createApiResponse)(false, undefined, message, undefined, undefined);
    if (details) {
        response.details = details;
    }
    if (config_1.config.nodeEnv === 'development' && !(error instanceof shared_utils_1.AppError)) {
        response.stack = error.stack;
    }
    res.status(statusCode).json(response);
};
exports.errorHandler = errorHandler;
const asyncHandler = (fn) => {
    return (req, res, next) => {
        Promise.resolve(fn(req, res, next)).catch(next);
    };
};
exports.asyncHandler = asyncHandler;
//# sourceMappingURL=errorHandler.js.map