import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, Tabs<PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  DollarSign, Package, Users, TrendingUp, TrendingDown, 
  AlertTriangle, CheckCircle, Clock, ArrowRight, 
  Smartphone, Tablet, RefreshCw, Bell, Search,
  BarChart3, PieChart, Activity, Target, Zap
} from 'lucide-react';

interface MobileMetric {
  id: string;
  title: string;
  value: string;
  change: string;
  trend: 'up' | 'down' | 'stable';
  icon: React.ReactNode;
  color: string;
  priority: 'high' | 'medium' | 'low';
}

interface MobileAlert {
  id: string;
  title: string;
  description: string;
  severity: 'critical' | 'warning' | 'info';
  timestamp: Date;
  domain: string;
}

interface QuickAction {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  href: string;
  badge?: string;
}

export const MobileDashboard: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('overview');
  const [metrics, setMetrics] = useState<MobileMetric[]>([]);
  const [alerts, setAlerts] = useState<MobileAlert[]>([]);

  useEffect(() => {
    loadMobileData();
  }, []);

  const loadMobileData = async () => {
    setLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const mockMetrics: MobileMetric[] = [
        {
          id: 'revenue',
          title: 'Revenue',
          value: '$2.8M',
          change: '+12.3%',
          trend: 'up',
          icon: <DollarSign className="w-5 h-5" />,
          color: 'text-green-600',
          priority: 'high'
        },
        {
          id: 'collections',
          title: 'Collections',
          value: '87.3%',
          change: '****%',
          trend: 'up',
          icon: <TrendingUp className="w-5 h-5" />,
          color: 'text-blue-600',
          priority: 'high'
        },
        {
          id: 'shipments',
          title: 'Active Shipments',
          value: '156',
          change: '+8',
          trend: 'up',
          icon: <Package className="w-5 h-5" />,
          color: 'text-purple-600',
          priority: 'medium'
        },
        {
          id: 'customers',
          title: 'Customer Satisfaction',
          value: '4.2/5',
          change: '+0.1',
          trend: 'up',
          icon: <Users className="w-5 h-5" />,
          color: 'text-orange-600',
          priority: 'medium'
        },
        {
          id: 'otd',
          title: 'On-Time Delivery',
          value: '92.1%',
          change: '****%',
          trend: 'up',
          icon: <CheckCircle className="w-5 h-5" />,
          color: 'text-green-600',
          priority: 'high'
        },
        {
          id: 'exceptions',
          title: 'Exceptions',
          value: '7',
          change: '-2',
          trend: 'down',
          icon: <AlertTriangle className="w-5 h-5" />,
          color: 'text-red-600',
          priority: 'high'
        }
      ];

      const mockAlerts: MobileAlert[] = [
        {
          id: 'alert-1',
          title: 'Payment Overdue',
          description: 'Acme Corp payment 15 days overdue - $75,000',
          severity: 'critical',
          timestamp: new Date('2024-02-10T10:30:00'),
          domain: 'AR'
        },
        {
          id: 'alert-2',
          title: 'Shipment Delay',
          description: 'Container MSKU123456 delayed at Port of LA',
          severity: 'warning',
          timestamp: new Date('2024-02-10T09:15:00'),
          domain: 'Supply Chain'
        },
        {
          id: 'alert-3',
          title: 'Credit Limit Exceeded',
          description: 'TechFlow Inc exceeded credit limit by $25,000',
          severity: 'warning',
          timestamp: new Date('2024-02-10T08:45:00'),
          domain: 'AR'
        }
      ];

      setMetrics(mockMetrics);
      setAlerts(mockAlerts);
    } catch (error) {
      console.error('Failed to load mobile data:', error);
    } finally {
      setLoading(false);
    }
  };

  const quickActions: QuickAction[] = [
    {
      id: 'ar-dashboard',
      title: 'AR Dashboard',
      description: 'View receivables overview',
      icon: <DollarSign className="w-6 h-6" />,
      href: '/ar/dashboard'
    },
    {
      id: 'shipment-tracking',
      title: 'Track Shipments',
      description: 'Monitor active shipments',
      icon: <Package className="w-6 h-6" />,
      href: '/supply-chain/tracking'
    },
    {
      id: 'customer-360',
      title: 'Customer 360',
      description: 'Comprehensive customer view',
      icon: <Users className="w-6 h-6" />,
      href: '/customers/360'
    },
    {
      id: 'analytics',
      title: 'Analytics',
      description: 'Advanced insights & reports',
      icon: <BarChart3 className="w-6 h-6" />,
      href: '/analytics'
    }
  ];

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'border-red-200 bg-red-50';
      case 'warning': return 'border-yellow-200 bg-yellow-50';
      case 'info': return 'border-blue-200 bg-blue-50';
      default: return 'border-gray-200 bg-gray-50';
    }
  };

  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case 'critical': return <AlertTriangle className="w-4 h-4 text-red-600" />;
      case 'warning': return <Clock className="w-4 h-4 text-yellow-600" />;
      case 'info': return <CheckCircle className="w-4 h-4 text-blue-600" />;
      default: return <AlertTriangle className="w-4 h-4 text-gray-600" />;
    }
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up': return <TrendingUp className="w-4 h-4 text-green-600" />;
      case 'down': return <TrendingDown className="w-4 h-4 text-red-600" />;
      default: return <Activity className="w-4 h-4 text-gray-600" />;
    }
  };

  const formatTimestamp = (date: Date) => {
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMins / 60);
    
    if (diffMins < 60) {
      return `${diffMins}m ago`;
    } else if (diffHours < 24) {
      return `${diffHours}h ago`;
    } else {
      return date.toLocaleDateString();
    }
  };

  if (loading && metrics.length === 0) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="mt-2 text-muted-foreground">Loading dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4 pb-20"> {/* Extra padding for mobile navigation */}
      {/* Mobile Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold">Dashboard</h1>
          <p className="text-sm text-muted-foreground">Business Intelligence Overview</p>
        </div>
        <div className="flex items-center gap-2">
          <Button size="sm" variant="outline" onClick={loadMobileData} disabled={loading}>
            <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
          </Button>
          <Button size="sm" variant="outline">
            <Bell className="w-4 h-4" />
          </Button>
          <Button size="sm" variant="outline">
            <Search className="w-4 h-4" />
          </Button>
        </div>
      </div>

      {/* Critical Alerts */}
      {alerts.filter(alert => alert.severity === 'critical').length > 0 && (
        <Alert className="border-red-200 bg-red-50">
          <AlertTriangle className="h-4 w-4 text-red-600" />
          <AlertDescription className="text-red-800">
            {alerts.filter(alert => alert.severity === 'critical').length} critical alert(s) require immediate attention
          </AlertDescription>
        </Alert>
      )}

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="alerts">
            Alerts
            {alerts.length > 0 && (
              <Badge variant="destructive" className="ml-1 text-xs">
                {alerts.length}
              </Badge>
            )}
          </TabsTrigger>
          <TabsTrigger value="actions">Actions</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          {/* Key Metrics Grid */}
          <div className="grid grid-cols-2 gap-3">
            {metrics.slice(0, 6).map((metric) => (
              <Card key={metric.id} className="p-3">
                <div className="flex items-center justify-between mb-2">
                  <div className={metric.color}>
                    {metric.icon}
                  </div>
                  {getTrendIcon(metric.trend)}
                </div>
                <div className="space-y-1">
                  <div className="text-lg font-bold">{metric.value}</div>
                  <div className="text-xs text-muted-foreground">{metric.title}</div>
                  <div className={`text-xs font-medium ${
                    metric.trend === 'up' ? 'text-green-600' : 
                    metric.trend === 'down' ? 'text-red-600' : 
                    'text-gray-600'
                  }`}>
                    {metric.change}
                  </div>
                </div>
              </Card>
            ))}
          </div>

          {/* Performance Summary */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-lg">Performance Summary</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-sm">Collection Rate</span>
                <div className="flex items-center gap-2">
                  <Progress value={87.3} className="w-16 h-2" />
                  <span className="text-sm font-medium">87.3%</span>
                </div>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm">On-Time Delivery</span>
                <div className="flex items-center gap-2">
                  <Progress value={92.1} className="w-16 h-2" />
                  <span className="text-sm font-medium">92.1%</span>
                </div>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm">Customer Satisfaction</span>
                <div className="flex items-center gap-2">
                  <Progress value={84} className="w-16 h-2" />
                  <span className="text-sm font-medium">4.2/5</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Recent Activity */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-lg">Recent Activity</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex items-center gap-3 text-sm">
                <CheckCircle className="w-4 h-4 text-green-600 flex-shrink-0" />
                <div className="flex-1">
                  <div className="font-medium">Payment received from TechFlow Inc</div>
                  <div className="text-muted-foreground">$125,000 • 2h ago</div>
                </div>
              </div>
              <div className="flex items-center gap-3 text-sm">
                <Package className="w-4 h-4 text-blue-600 flex-shrink-0" />
                <div className="flex-1">
                  <div className="font-medium">Shipment MSKU789012 delivered</div>
                  <div className="text-muted-foreground">Los Angeles • 4h ago</div>
                </div>
              </div>
              <div className="flex items-center gap-3 text-sm">
                <AlertTriangle className="w-4 h-4 text-yellow-600 flex-shrink-0" />
                <div className="flex-1">
                  <div className="font-medium">Credit limit review required</div>
                  <div className="text-muted-foreground">Global Manufacturing • 6h ago</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="alerts" className="space-y-3">
          {alerts.map((alert) => (
            <Card key={alert.id} className={`border ${getSeverityColor(alert.severity)}`}>
              <CardContent className="p-4">
                <div className="flex items-start gap-3">
                  {getSeverityIcon(alert.severity)}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between mb-1">
                      <h3 className="font-medium text-sm">{alert.title}</h3>
                      <Badge variant="outline" className="text-xs">
                        {alert.domain}
                      </Badge>
                    </div>
                    <p className="text-sm text-muted-foreground mb-2">{alert.description}</p>
                    <div className="flex items-center justify-between">
                      <span className="text-xs text-muted-foreground">
                        {formatTimestamp(alert.timestamp)}
                      </span>
                      <Button size="sm" variant="outline" className="h-7 text-xs">
                        View Details
                        <ArrowRight className="w-3 h-3 ml-1" />
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </TabsContent>

        <TabsContent value="actions" className="space-y-3">
          <div className="grid grid-cols-1 gap-3">
            {quickActions.map((action) => (
              <Card key={action.id} className="p-4">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center text-primary">
                    {action.icon}
                  </div>
                  <div className="flex-1">
                    <h3 className="font-medium">{action.title}</h3>
                    <p className="text-sm text-muted-foreground">{action.description}</p>
                  </div>
                  <ArrowRight className="w-5 h-5 text-muted-foreground" />
                </div>
              </Card>
            ))}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};
