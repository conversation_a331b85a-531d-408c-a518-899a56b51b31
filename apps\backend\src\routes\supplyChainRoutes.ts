import { Router, Request, Response } from 'express';
import { body, param, query, validationResult } from 'express-validator';
import { authenticateToken, requirePermissions } from '../middleware/auth';
import { supplyChainExceptionService, Shipment, SupplyChainException } from '../services/supplyChainExceptionService';
import { fishbowlService } from '../services/fishbowlService';
import { crossDomainRulesService } from '../services/crossDomainRulesService';
import { HILWorkflowService } from '../services/hilWorkflowService';
import { AppError } from '@ar-scia/shared-utils';

const router = Router();
const hilWorkflowService = new HILWorkflowService();

// Apply authentication to all routes
router.use(authenticateToken);

/**
 * POST /api/v1/supply-chain/exceptions/detect
 * Detect exceptions for a shipment
 */
router.post('/exceptions/detect',
  requirePermissions(['supply_chain:read']),
  [
    body('shipment').isObject().withMessage('Shipment data is required'),
    body('shipment.id').notEmpty().withMessage('Shipment ID is required'),
    body('shipment.orderNumber').notEmpty().withMessage('Order number is required'),
    body('shipment.customerId').notEmpty().withMessage('Customer ID is required'),
    body('shipment.estimatedArrival').isISO8601().withMessage('Valid estimated arrival date is required')
  ],
  async (req: Request, res: Response) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: 'Validation failed',
          details: errors.array()
        });
      }

      const { shipment } = req.body;
      
      // Convert date strings to Date objects
      shipment.estimatedDeparture = new Date(shipment.estimatedDeparture);
      shipment.estimatedArrival = new Date(shipment.estimatedArrival);
      if (shipment.actualDeparture) shipment.actualDeparture = new Date(shipment.actualDeparture);
      if (shipment.actualArrival) shipment.actualArrival = new Date(shipment.actualArrival);

      const exceptions = await supplyChainExceptionService.detectExceptions(shipment as Shipment);

      res.json({
        success: true,
        data: {
          shipmentId: shipment.id,
          exceptionsFound: exceptions.length,
          exceptions: exceptions,
          summary: {
            critical: exceptions.filter(e => e.severity === 'critical').length,
            high: exceptions.filter(e => e.severity === 'high').length,
            medium: exceptions.filter(e => e.severity === 'medium').length,
            low: exceptions.filter(e => e.severity === 'low').length
          }
        },
        message: `Found ${exceptions.length} exceptions for shipment ${shipment.orderNumber}`
      });
    } catch (error) {
      console.error('Exception detection error:', error);
      if (error instanceof AppError) {
        res.status(error.statusCode).json({
          success: false,
          error: error.message
        });
      } else {
        res.status(500).json({
          success: false,
          error: 'Failed to detect exceptions'
        });
      }
    }
  }
);

/**
 * GET /api/v1/supply-chain/exceptions/shipment/:shipmentId
 * Get exceptions for a specific shipment
 */
router.get('/exceptions/shipment/:shipmentId',
  requirePermissions(['supply_chain:read']),
  [
    param('shipmentId').notEmpty().withMessage('Shipment ID is required')
  ],
  async (req: Request, res: Response) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: 'Validation failed',
          details: errors.array()
        });
      }

      const { shipmentId } = req.params;
      const exceptions = await supplyChainExceptionService.getExceptionsByShipment(shipmentId);

      res.json({
        success: true,
        data: exceptions,
        message: 'Shipment exceptions retrieved successfully'
      });
    } catch (error) {
      console.error('Get shipment exceptions error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve shipment exceptions'
      });
    }
  }
);

/**
 * GET /api/v1/supply-chain/exceptions/customer/:customerId
 * Get exceptions for a specific customer
 */
router.get('/exceptions/customer/:customerId',
  requirePermissions(['supply_chain:read']),
  [
    param('customerId').notEmpty().withMessage('Customer ID is required')
  ],
  async (req: Request, res: Response) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: 'Validation failed',
          details: errors.array()
        });
      }

      const { customerId } = req.params;
      const exceptions = await supplyChainExceptionService.getExceptionsByCustomer(customerId);

      res.json({
        success: true,
        data: exceptions,
        message: 'Customer exceptions retrieved successfully'
      });
    } catch (error) {
      console.error('Get customer exceptions error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve customer exceptions'
      });
    }
  }
);

/**
 * PUT /api/v1/supply-chain/exceptions/:exceptionId
 * Update exception status
 */
router.put('/exceptions/:exceptionId',
  requirePermissions(['supply_chain:write']),
  [
    param('exceptionId').notEmpty().withMessage('Exception ID is required'),
    body('status').isIn(['open', 'investigating', 'resolved', 'escalated']).withMessage('Invalid status'),
    body('resolution').optional().isString(),
    body('assignedTo').optional().isString()
  ],
  async (req: Request, res: Response) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: 'Validation failed',
          details: errors.array()
        });
      }

      const { exceptionId } = req.params;
      const { status, resolution, assignedTo } = req.body;

      const updatedException = await supplyChainExceptionService.updateExceptionStatus(
        exceptionId,
        status,
        resolution,
        assignedTo
      );

      res.json({
        success: true,
        data: updatedException,
        message: 'Exception updated successfully'
      });
    } catch (error) {
      console.error('Update exception error:', error);
      if (error instanceof AppError) {
        res.status(error.statusCode).json({
          success: false,
          error: error.message
        });
      } else {
        res.status(500).json({
          success: false,
          error: 'Failed to update exception'
        });
      }
    }
  }
);

/**
 * POST /api/v1/supply-chain/exceptions/manual
 * Create manual exception
 */
router.post('/exceptions/manual',
  requirePermissions(['supply_chain:write']),
  [
    body('shipmentId').notEmpty().withMessage('Shipment ID is required'),
    body('type').isIn(['delay', 'missing_document', 'customs_hold', 'weather_delay', 'port_congestion', 'vessel_breakdown', 'documentation_error', 'quality_issue']).withMessage('Invalid exception type'),
    body('severity').isIn(['low', 'medium', 'high', 'critical']).withMessage('Invalid severity'),
    body('title').notEmpty().withMessage('Title is required'),
    body('description').notEmpty().withMessage('Description is required')
  ],
  async (req: Request, res: Response) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: 'Validation failed',
          details: errors.array()
        });
      }

      const { shipmentId, ...exceptionData } = req.body;
      const exception = await supplyChainExceptionService.createManualException(shipmentId, exceptionData);

      res.status(201).json({
        success: true,
        data: exception,
        message: 'Manual exception created successfully'
      });
    } catch (error) {
      console.error('Create manual exception error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to create manual exception'
      });
    }
  }
);

/**
 * GET /api/v1/supply-chain/exceptions/metrics
 * Get exception metrics and analytics
 */
router.get('/exceptions/metrics',
  requirePermissions(['supply_chain:read']),
  [
    query('startDate').optional().isISO8601().withMessage('Invalid start date'),
    query('endDate').optional().isISO8601().withMessage('Invalid end date')
  ],
  async (req: Request, res: Response) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: 'Validation failed',
          details: errors.array()
        });
      }

      const dateRange = req.query.startDate && req.query.endDate ? {
        start: new Date(req.query.startDate as string),
        end: new Date(req.query.endDate as string)
      } : undefined;

      const metrics = await supplyChainExceptionService.getExceptionMetrics(dateRange);

      res.json({
        success: true,
        data: metrics,
        message: 'Exception metrics retrieved successfully'
      });
    } catch (error) {
      console.error('Get exception metrics error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve exception metrics'
      });
    }
  }
);

/**
 * POST /api/v1/supply-chain/exceptions/predict
 * Predict potential exceptions for a shipment
 */
router.post('/exceptions/predict',
  requirePermissions(['supply_chain:read']),
  [
    body('shipment').isObject().withMessage('Shipment data is required'),
    body('shipment.id').notEmpty().withMessage('Shipment ID is required')
  ],
  async (req: Request, res: Response) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: 'Validation failed',
          details: errors.array()
        });
      }

      const { shipment } = req.body;
      
      // Convert date strings to Date objects
      shipment.estimatedDeparture = new Date(shipment.estimatedDeparture);
      shipment.estimatedArrival = new Date(shipment.estimatedArrival);
      if (shipment.actualDeparture) shipment.actualDeparture = new Date(shipment.actualDeparture);
      if (shipment.actualArrival) shipment.actualArrival = new Date(shipment.actualArrival);

      const predictions = await supplyChainExceptionService.predictPotentialExceptions(shipment as Shipment);

      res.json({
        success: true,
        data: predictions,
        message: 'Exception predictions generated successfully'
      });
    } catch (error) {
      console.error('Exception prediction error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to predict exceptions'
      });
    }
  }
);

/**
 * POST /api/v1/supply-chain/exceptions/:exceptionId/recommendations
 * Get resolution recommendations for an exception
 */
router.post('/exceptions/:exceptionId/recommendations',
  requirePermissions(['supply_chain:read']),
  [
    param('exceptionId').notEmpty().withMessage('Exception ID is required'),
    body('exception').isObject().withMessage('Exception data is required')
  ],
  async (req: Request, res: Response) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: 'Validation failed',
          details: errors.array()
        });
      }

      const { exception } = req.body;
      
      // Convert date strings to Date objects
      exception.detectedDate = new Date(exception.detectedDate);
      if (exception.expectedResolutionDate) exception.expectedResolutionDate = new Date(exception.expectedResolutionDate);
      if (exception.actualResolutionDate) exception.actualResolutionDate = new Date(exception.actualResolutionDate);

      const recommendations = await supplyChainExceptionService.generateResolutionRecommendations(exception as SupplyChainException);

      res.json({
        success: true,
        data: recommendations,
        message: 'Resolution recommendations generated successfully'
      });
    } catch (error) {
      console.error('Generate recommendations error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to generate recommendations'
      });
    }
  }
);

/**
 * GET /api/v1/supply-chain/fishbowl/orders
 * Get orders from Fishbowl
 */
router.get('/fishbowl/orders',
  requirePermissions(['supply_chain:read']),
  [
    query('status').optional().isString(),
    query('customerId').optional().isInt(),
    query('dateFrom').optional().isISO8601(),
    query('dateTo').optional().isISO8601(),
    query('limit').optional().isInt({ min: 1, max: 1000 }),
    query('offset').optional().isInt({ min: 0 })
  ],
  async (req: Request, res: Response) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: 'Validation failed',
          details: errors.array()
        });
      }

      const filters = {
        status: req.query.status as string,
        customerId: req.query.customerId ? parseInt(req.query.customerId as string) : undefined,
        dateFrom: req.query.dateFrom ? new Date(req.query.dateFrom as string) : undefined,
        dateTo: req.query.dateTo ? new Date(req.query.dateTo as string) : undefined,
        limit: req.query.limit ? parseInt(req.query.limit as string) : undefined,
        offset: req.query.offset ? parseInt(req.query.offset as string) : undefined
      };

      const orders = await fishbowlService.getOrders(filters);

      res.json({
        success: true,
        data: orders,
        message: 'Orders retrieved from Fishbowl successfully'
      });
    } catch (error) {
      console.error('Get Fishbowl orders error:', error);
      if (error instanceof AppError) {
        res.status(error.statusCode).json({
          success: false,
          error: error.message
        });
      } else {
        res.status(500).json({
          success: false,
          error: 'Failed to retrieve orders from Fishbowl'
        });
      }
    }
  }
);

/**
 * GET /api/v1/supply-chain/fishbowl/orders/:orderId
 * Get specific order from Fishbowl
 */
router.get('/fishbowl/orders/:orderId',
  requirePermissions(['supply_chain:read']),
  [
    param('orderId').isInt().withMessage('Order ID must be an integer')
  ],
  async (req: Request, res: Response) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: 'Validation failed',
          details: errors.array()
        });
      }

      const orderId = parseInt(req.params.orderId);
      const order = await fishbowlService.getOrderById(orderId);

      if (!order) {
        return res.status(404).json({
          success: false,
          error: 'Order not found'
        });
      }

      res.json({
        success: true,
        data: order,
        message: 'Order retrieved from Fishbowl successfully'
      });
    } catch (error) {
      console.error('Get Fishbowl order error:', error);
      if (error instanceof AppError) {
        res.status(error.statusCode).json({
          success: false,
          error: error.message
        });
      } else {
        res.status(500).json({
          success: false,
          error: 'Failed to retrieve order from Fishbowl'
        });
      }
    }
  }
);

/**
 * GET /api/v1/supply-chain/fishbowl/shipments
 * Get shipments from Fishbowl
 */
router.get('/fishbowl/shipments',
  requirePermissions(['supply_chain:read']),
  [
    query('status').optional().isString(),
    query('orderId').optional().isInt(),
    query('customerId').optional().isInt(),
    query('dateFrom').optional().isISO8601(),
    query('dateTo').optional().isISO8601(),
    query('limit').optional().isInt({ min: 1, max: 1000 }),
    query('offset').optional().isInt({ min: 0 })
  ],
  async (req: Request, res: Response) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: 'Validation failed',
          details: errors.array()
        });
      }

      const filters = {
        status: req.query.status as string,
        orderId: req.query.orderId ? parseInt(req.query.orderId as string) : undefined,
        customerId: req.query.customerId ? parseInt(req.query.customerId as string) : undefined,
        dateFrom: req.query.dateFrom ? new Date(req.query.dateFrom as string) : undefined,
        dateTo: req.query.dateTo ? new Date(req.query.dateTo as string) : undefined,
        limit: req.query.limit ? parseInt(req.query.limit as string) : undefined,
        offset: req.query.offset ? parseInt(req.query.offset as string) : undefined
      };

      const shipments = await fishbowlService.getShipments(filters);

      res.json({
        success: true,
        data: shipments,
        message: 'Shipments retrieved from Fishbowl successfully'
      });
    } catch (error) {
      console.error('Get Fishbowl shipments error:', error);
      if (error instanceof AppError) {
        res.status(error.statusCode).json({
          success: false,
          error: error.message
        });
      } else {
        res.status(500).json({
          success: false,
          error: 'Failed to retrieve shipments from Fishbowl'
        });
      }
    }
  }
);

/**
 * GET /api/v1/supply-chain/fishbowl/inventory
 * Get inventory from Fishbowl
 */
router.get('/fishbowl/inventory',
  requirePermissions(['supply_chain:read']),
  [
    query('partNumber').optional().isString(),
    query('locationId').optional().isInt(),
    query('lowStock').optional().isBoolean(),
    query('limit').optional().isInt({ min: 1, max: 5000 }),
    query('offset').optional().isInt({ min: 0 })
  ],
  async (req: Request, res: Response) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: 'Validation failed',
          details: errors.array()
        });
      }

      const filters = {
        partNumber: req.query.partNumber as string,
        locationId: req.query.locationId ? parseInt(req.query.locationId as string) : undefined,
        lowStock: req.query.lowStock === 'true',
        limit: req.query.limit ? parseInt(req.query.limit as string) : undefined,
        offset: req.query.offset ? parseInt(req.query.offset as string) : undefined
      };

      const inventory = await fishbowlService.getInventory(filters);

      res.json({
        success: true,
        data: inventory,
        message: 'Inventory retrieved from Fishbowl successfully'
      });
    } catch (error) {
      console.error('Get Fishbowl inventory error:', error);
      if (error instanceof AppError) {
        res.status(error.statusCode).json({
          success: false,
          error: error.message
        });
      } else {
        res.status(500).json({
          success: false,
          error: 'Failed to retrieve inventory from Fishbowl'
        });
      }
    }
  }
);

/**
 * GET /api/v1/supply-chain/fishbowl/customers
 * Get customers from Fishbowl
 */
router.get('/fishbowl/customers',
  requirePermissions(['supply_chain:read']),
  [
    query('status').optional().isString(),
    query('type').optional().isString(),
    query('limit').optional().isInt({ min: 1, max: 1000 }),
    query('offset').optional().isInt({ min: 0 })
  ],
  async (req: Request, res: Response) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: 'Validation failed',
          details: errors.array()
        });
      }

      const filters = {
        status: req.query.status as string,
        type: req.query.type as string,
        limit: req.query.limit ? parseInt(req.query.limit as string) : undefined,
        offset: req.query.offset ? parseInt(req.query.offset as string) : undefined
      };

      const customers = await fishbowlService.getCustomers(filters);

      res.json({
        success: true,
        data: customers,
        message: 'Customers retrieved from Fishbowl successfully'
      });
    } catch (error) {
      console.error('Get Fishbowl customers error:', error);
      if (error instanceof AppError) {
        res.status(error.statusCode).json({
          success: false,
          error: error.message
        });
      } else {
        res.status(500).json({
          success: false,
          error: 'Failed to retrieve customers from Fishbowl'
        });
      }
    }
  }
);

/**
 * POST /api/v1/supply-chain/fishbowl/sync
 * Perform synchronization with Fishbowl
 */
router.post('/fishbowl/sync',
  requirePermissions(['supply_chain:write']),
  [
    body('type').isIn(['orders', 'shipments', 'inventory', 'customers', 'full']).withMessage('Invalid sync type'),
    body('lastSyncTime').optional().isISO8601().withMessage('Invalid last sync time')
  ],
  async (req: Request, res: Response) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: 'Validation failed',
          details: errors.array()
        });
      }

      const { type, lastSyncTime } = req.body;
      const lastSync = lastSyncTime ? new Date(lastSyncTime) : undefined;

      let result;

      switch (type) {
        case 'orders':
          result = await fishbowlService.syncOrders(lastSync);
          break;
        case 'shipments':
          result = await fishbowlService.syncShipments(lastSync);
          break;
        case 'inventory':
          result = await fishbowlService.syncInventory(lastSync);
          break;
        case 'customers':
          result = await fishbowlService.syncCustomers(lastSync);
          break;
        case 'full':
          result = await fishbowlService.performFullSync();
          break;
        default:
          return res.status(400).json({
            success: false,
            error: 'Invalid sync type'
          });
      }

      res.json({
        success: true,
        data: result,
        message: `${type} synchronization completed successfully`
      });
    } catch (error) {
      console.error('Fishbowl sync error:', error);
      if (error instanceof AppError) {
        res.status(error.statusCode).json({
          success: false,
          error: error.message
        });
      } else {
        res.status(500).json({
          success: false,
          error: 'Failed to synchronize with Fishbowl'
        });
      }
    }
  }
);

/**
 * GET /api/v1/supply-chain/fishbowl/health
 * Check Fishbowl connection health
 */
router.get('/fishbowl/health',
  requirePermissions(['supply_chain:read']),
  async (req: Request, res: Response) => {
    try {
      const health = await fishbowlService.healthCheck();

      res.json({
        success: true,
        data: health,
        message: 'Fishbowl health check completed'
      });
    } catch (error) {
      console.error('Fishbowl health check error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to check Fishbowl health'
      });
    }
  }
);

/**
 * POST /api/v1/supply-chain/cross-domain/evaluate
 * Evaluate cross-domain business rules for a customer
 */
router.post('/cross-domain/evaluate',
  requirePermissions(['supply_chain:read', 'ar:read']),
  [
    body('customerId').isString().notEmpty().withMessage('Customer ID is required'),
    body('context').isObject().withMessage('Context object is required'),
    body('context.customer').isObject().withMessage('Customer data is required'),
    body('context.arData').isObject().withMessage('AR data is required'),
    body('context.supplyChainData').isObject().withMessage('Supply chain data is required')
  ],
  async (req: Request, res: Response) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: 'Validation failed',
          details: errors.array()
        });
      }

      const { customerId, context } = req.body;

      const decision = await crossDomainRulesService.evaluateCustomerRules(context);

      res.json({
        success: true,
        data: decision,
        message: 'Cross-domain rules evaluated successfully'
      });
    } catch (error) {
      console.error('Cross-domain rule evaluation error:', error);
      if (error instanceof AppError) {
        res.status(error.statusCode).json({
          success: false,
          error: error.message
        });
      } else {
        res.status(500).json({
          success: false,
          error: 'Failed to evaluate cross-domain rules'
        });
      }
    }
  }
);

/**
 * POST /api/v1/supply-chain/cross-domain/execute
 * Execute cross-domain actions from a decision
 */
router.post('/cross-domain/execute',
  requirePermissions(['supply_chain:write', 'ar:write']),
  [
    body('decision').isObject().withMessage('Decision object is required'),
    body('decision.customerId').isString().notEmpty().withMessage('Customer ID is required'),
    body('decision.finalRecommendations').isArray().withMessage('Recommendations array is required')
  ],
  async (req: Request, res: Response) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: 'Validation failed',
          details: errors.array()
        });
      }

      const { decision } = req.body;

      const executedActions = await crossDomainRulesService.executeActions(decision);

      res.json({
        success: true,
        data: {
          executedActions,
          timestamp: new Date(),
          customerId: decision.customerId
        },
        message: 'Cross-domain actions executed successfully'
      });
    } catch (error) {
      console.error('Cross-domain action execution error:', error);
      if (error instanceof AppError) {
        res.status(error.statusCode).json({
          success: false,
          error: error.message
        });
      } else {
        res.status(500).json({
          success: false,
          error: 'Failed to execute cross-domain actions'
        });
      }
    }
  }
);

/**
 * GET /api/v1/supply-chain/cross-domain/customer/:customerId/analysis
 * Get comprehensive cross-domain analysis for a customer
 */
router.get('/cross-domain/customer/:customerId/analysis',
  requirePermissions(['supply_chain:read', 'ar:read']),
  [
    param('customerId').isString().notEmpty().withMessage('Customer ID is required')
  ],
  async (req: Request, res: Response) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: 'Validation failed',
          details: errors.array()
        });
      }

      const { customerId } = req.params;

      // In a real implementation, fetch customer data from multiple sources
      const mockContext = {
        customer: {
          id: customerId,
          name: 'Sample Customer Corp',
          priority: 'high' as const,
          paymentHistory: {
            averageDaysToPayment: 35,
            paymentReliability: 85,
            totalPaidAmount: 2500000,
            disputeHistory: 2
          },
          shipmentHistory: {
            totalShipments: 156,
            onTimeDeliveryRate: 92,
            averageShipmentValue: 45000,
            exceptionRate: 8
          },
          riskScore: 25,
          totalOutstanding: 125000,
          averagePaymentDays: 35,
          relationshipValue: 3200000
        },
        arData: {
          outstandingInvoices: [
            {
              id: 'INV-001',
              amount: 75000,
              dueDate: new Date('2024-01-15'),
              daysPastDue: 10,
              status: 'overdue'
            }
          ],
          overdueAmount: 75000,
          daysPastDue: 10,
          paymentCommitments: [],
          recentInteractions: []
        },
        supplyChainData: {
          activeShipments: [
            {
              id: 'SHIP-001',
              value: 85000,
              status: 'in_transit',
              estimatedDelivery: new Date('2024-02-15'),
              priority: 'high'
            }
          ],
          pendingOrders: [],
          exceptions: [],
          performanceMetrics: {
            onTimeDelivery: 92,
            averageTransitTime: 18,
            exceptionRate: 8,
            customerSatisfaction: 4.2
          }
        },
        businessContext: {
          seasonality: 'Q1',
          marketConditions: 'stable',
          companyPriorities: ['customer_retention', 'cash_flow'],
          riskTolerance: 'medium' as const
        }
      };

      const decision = await crossDomainRulesService.evaluateCustomerRules(mockContext);

      res.json({
        success: true,
        data: {
          customerId,
          analysis: decision,
          context: mockContext,
          timestamp: new Date()
        },
        message: 'Cross-domain analysis completed successfully'
      });
    } catch (error) {
      console.error('Cross-domain analysis error:', error);
      if (error instanceof AppError) {
        res.status(error.statusCode).json({
          success: false,
          error: error.message
        });
      } else {
        res.status(500).json({
          success: false,
          error: 'Failed to perform cross-domain analysis'
        });
      }
    }
  }
);

// ===== HIL WORKFLOW ENDPOINTS =====

/**
 * GET /api/v1/supply-chain/hil/tasks
 * Get HIL tasks for the current user
 */
router.get('/hil/tasks',
  requirePermissions(['supply_chain:read']),
  [
    query('domain').optional().isIn(['ar', 'supply_chain', 'customer', 'cross_domain']),
    query('priority').optional().isIn(['critical', 'high', 'medium', 'low']),
    query('status').optional().isIn(['pending', 'in_progress', 'completed', 'escalated', 'cancelled']),
    query('limit').optional().isInt({ min: 1, max: 100 }),
    query('offset').optional().isInt({ min: 0 })
  ],
  async (req: Request, res: Response) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: 'Validation failed',
          details: errors.array()
        });
      }

      const userId = (req as any).user?.id || 'current_user';
      const filters = {
        domain: req.query.domain as string,
        priority: req.query.priority as string,
        status: req.query.status as string
      };

      const tasks = await hilWorkflowService.getTasksForUser(userId, filters);

      res.json({
        success: true,
        data: tasks,
        message: 'HIL tasks retrieved successfully'
      });
    } catch (error) {
      console.error('Get HIL tasks error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve HIL tasks'
      });
    }
  }
);

/**
 * POST /api/v1/supply-chain/hil/tasks
 * Create a new HIL task
 */
router.post('/hil/tasks',
  requirePermissions(['supply_chain:write']),
  [
    body('type').isIn(['approval', 'review', 'decision', 'exception_handling', 'validation']).withMessage('Invalid task type'),
    body('title').isString().notEmpty().withMessage('Title is required'),
    body('description').isString().notEmpty().withMessage('Description is required'),
    body('domain').isIn(['ar', 'supply_chain', 'customer', 'cross_domain']).withMessage('Invalid domain'),
    body('context').isObject().withMessage('Context object is required')
  ],
  async (req: Request, res: Response) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: 'Validation failed',
          details: errors.array()
        });
      }

      const { type, title, description, domain, context } = req.body;

      const task = await hilWorkflowService.createHILTask(
        type,
        title,
        description,
        context,
        domain
      );

      res.status(201).json({
        success: true,
        data: task,
        message: 'HIL task created successfully'
      });
    } catch (error) {
      console.error('Create HIL task error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to create HIL task'
      });
    }
  }
);

/**
 * GET /api/v1/supply-chain/hil/tasks/:taskId/guidance
 * Get contextual guidance for a HIL task
 */
router.get('/hil/tasks/:taskId/guidance',
  requirePermissions(['supply_chain:read']),
  [
    param('taskId').isString().notEmpty().withMessage('Task ID is required')
  ],
  async (req: Request, res: Response) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: 'Validation failed',
          details: errors.array()
        });
      }

      const { taskId } = req.params;
      const userId = (req as any).user?.id || 'current_user';

      // Get the task first
      const tasks = await hilWorkflowService.getTasksForUser(userId);
      const task = tasks.find(t => t.id === taskId);

      if (!task) {
        return res.status(404).json({
          success: false,
          error: 'Task not found'
        });
      }

      const guidance = await hilWorkflowService.generateContextualGuidance(task);

      res.json({
        success: true,
        data: guidance,
        message: 'Contextual guidance generated successfully'
      });
    } catch (error) {
      console.error('Generate guidance error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to generate contextual guidance'
      });
    }
  }
);

/**
 * PUT /api/v1/supply-chain/hil/tasks/:taskId/status
 * Update HIL task status
 */
router.put('/hil/tasks/:taskId/status',
  requirePermissions(['supply_chain:write']),
  [
    param('taskId').isString().notEmpty().withMessage('Task ID is required'),
    body('status').isIn(['pending', 'in_progress', 'completed', 'escalated', 'cancelled']).withMessage('Invalid status'),
    body('assignedTo').optional().isString(),
    body('notes').optional().isString()
  ],
  async (req: Request, res: Response) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: 'Validation failed',
          details: errors.array()
        });
      }

      const { taskId } = req.params;
      const { status, assignedTo, notes } = req.body;

      await hilWorkflowService.updateTaskStatus(taskId, status, assignedTo, notes);

      res.json({
        success: true,
        message: 'Task status updated successfully'
      });
    } catch (error) {
      console.error('Update task status error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to update task status'
      });
    }
  }
);

/**
 * POST /api/v1/supply-chain/hil/tasks/prioritize
 * Prioritize multiple HIL tasks
 */
router.post('/hil/tasks/prioritize',
  requirePermissions(['supply_chain:read']),
  [
    body('taskIds').isArray().withMessage('Task IDs array is required'),
    body('taskIds.*').isString().withMessage('Each task ID must be a string')
  ],
  async (req: Request, res: Response) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: 'Validation failed',
          details: errors.array()
        });
      }

      const { taskIds } = req.body;
      const userId = (req as any).user?.id || 'current_user';

      // Get tasks for the user
      const allTasks = await hilWorkflowService.getTasksForUser(userId);
      const tasksToprioritize = allTasks.filter(task => taskIds.includes(task.id));

      const prioritizations = await hilWorkflowService.prioritizeTasks(tasksToprioritize);

      res.json({
        success: true,
        data: prioritizations,
        message: 'Tasks prioritized successfully'
      });
    } catch (error) {
      console.error('Prioritize tasks error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to prioritize tasks'
      });
    }
  }
);

export default router;
