"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthService = void 0;
const bcryptjs_1 = __importDefault(require("bcryptjs"));
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const client_1 = require("@prisma/client");
const shared_utils_1 = require("@ar-scia/shared-utils");
const config_1 = require("@/config/config");
const prisma = new client_1.PrismaClient();
class AuthService {
    async login(loginData) {
        const { email, password } = loginData;
        const user = await prisma.user.findUnique({
            where: { email },
            include: {
                roles: {
                    include: {
                        role: {
                            include: {
                                permissions: {
                                    include: {
                                        permission: true
                                    }
                                }
                            }
                        }
                    }
                }
            }
        });
        if (!user) {
            throw new shared_utils_1.AppError('Invalid credentials', 401);
        }
        if (!user.isActive) {
            throw new shared_utils_1.AppError('Account is deactivated', 401);
        }
        const isPasswordValid = await bcryptjs_1.default.compare(password, user.password);
        if (!isPasswordValid) {
            throw new shared_utils_1.AppError('Invalid credentials', 401);
        }
        const token = jsonwebtoken_1.default.sign({
            userId: user.id,
            email: user.email,
            roles: user.roles.map(ur => ur.role.name)
        }, config_1.config.jwtSecret, { expiresIn: config_1.config.jwtExpiresIn });
        const expiresAt = new Date();
        expiresAt.setTime(expiresAt.getTime() + 24 * 60 * 60 * 1000);
        await prisma.session.create({
            data: {
                userId: user.id,
                token,
                expiresAt
            }
        });
        const permissions = (0, shared_utils_1.extractPermissions)(user.roles.map(ur => ({
            permissions: ur.role.permissions.map(rp => rp.permission)
        })));
        const userResponse = {
            id: user.id,
            email: user.email,
            firstName: user.firstName,
            lastName: user.lastName,
            isActive: user.isActive,
            createdAt: user.createdAt,
            updatedAt: user.updatedAt,
            roles: user.roles.map(ur => ({
                userId: ur.userId,
                roleId: ur.roleId,
                user: {},
                role: {
                    id: ur.role.id,
                    name: ur.role.name,
                    description: ur.role.description,
                    permissions: ur.role.permissions.map(rp => rp.permission)
                }
            }))
        };
        return {
            user: userResponse,
            token,
            expiresAt
        };
    }
}
exports.AuthService = AuthService;
//# sourceMappingURL=authService.js.map