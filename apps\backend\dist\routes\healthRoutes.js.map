{"version": 3, "file": "healthRoutes.js", "sourceRoot": "", "sources": ["../../src/routes/healthRoutes.ts"], "names": [], "mappings": ";;;AAAA,qCAAiC;AACjC,2CAA8C;AAC9C,wDAA0D;AAC1D,4CAAyC;AAEzC,MAAM,MAAM,GAAG,IAAA,gBAAM,GAAE,CAAC;AAsCL,8BAAY;AArC/B,MAAM,MAAM,GAAG,IAAI,qBAAY,EAAE,CAAC;AAGlC,MAAM,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE;IACjC,IAAI,CAAC;QAEH,MAAM,MAAM,CAAC,SAAS,CAAA,UAAU,CAAC;QAEjC,MAAM,UAAU,GAAG;YACjB,MAAM,EAAE,SAAS;YACjB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,WAAW,EAAE,eAAM,CAAC,OAAO;YAC3B,OAAO,EAAE,OAAO;YAChB,QAAQ,EAAE;gBACR,QAAQ,EAAE,WAAW;gBACrB,GAAG,EAAE,SAAS;aACf;SACF,CAAC;QAEF,GAAG,CAAC,IAAI,CAAC,IAAA,gCAAiB,EAAC,IAAI,EAAE,UAAU,EAAE,SAAS,EAAE,mBAAmB,CAAC,CAAC,CAAC;IAChF,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,MAAM,UAAU,GAAG;YACjB,MAAM,EAAE,WAAW;YACnB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,WAAW,EAAE,eAAM,CAAC,OAAO;YAC3B,OAAO,EAAE,OAAO;YAChB,QAAQ,EAAE;gBACR,QAAQ,EAAE,cAAc;gBACxB,GAAG,EAAE,SAAS;aACf;YACD,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe;SAChE,CAAC;QAEF,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,IAAA,gCAAiB,EAAC,KAAK,EAAE,UAAU,EAAE,qBAAqB,CAAC,CAAC,CAAC;IACpF,CAAC;AACH,CAAC,CAAC,CAAC"}