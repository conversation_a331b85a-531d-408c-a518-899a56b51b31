import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import rateLimit from 'express-rate-limit';
import { config } from '@/config/config';
import { errorHandler } from '@/middleware/errorHandler';
import { requestLogger } from '@/middleware/requestLogger';
import { authRoutes } from '@/routes/authRoutes';
import { userRoutes } from '@/routes/userRoutes';
import { customerRoutes } from '@/routes/customerRoutes';
import { invoiceRoutes } from '@/routes/invoiceRoutes';
import { paymentRoutes } from '@/routes/paymentRoutes';
import { shipmentRoutes } from '@/routes/shipmentRoutes';
import { documentRoutes } from '@/routes/documentRoutes';
import { businessRuleRoutes } from '@/routes/businessRuleRoutes';
import { exceptionRoutes } from '@/routes/exceptionRoutes';
import { analyticsRoutes } from '@/routes/analyticsRoutes';
import { healthRoutes } from '@/routes/healthRoutes';
import aiRoutes from '@/routes/aiRoutes';
import emailRoutes from '@/routes/emailRoutes';
import responseRoutes from '@/routes/responseRoutes';
import supplyChainRoutes from '@/routes/supplyChainRoutes';

const app = express();

// Security middleware
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
    },
  },
  crossOriginEmbedderPolicy: false,
}));

// CORS configuration
app.use(cors({
  origin: config.corsOrigin,
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
}));

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: {
    error: 'Too many requests from this IP, please try again later.',
  },
  standardHeaders: true,
  legacyHeaders: false,
});

app.use('/api/', limiter);

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Request logging
app.use(requestLogger);

// Health check (before authentication)
app.use('/health', healthRoutes);

// API routes
const apiRouter = express.Router();

// Authentication routes (no auth required)
apiRouter.use('/auth', authRoutes);

// Protected routes
apiRouter.use('/users', userRoutes);
apiRouter.use('/customers', customerRoutes);
apiRouter.use('/invoices', invoiceRoutes);
apiRouter.use('/payments', paymentRoutes);
apiRouter.use('/shipments', shipmentRoutes);
apiRouter.use('/documents', documentRoutes);
apiRouter.use('/rules', businessRuleRoutes);
apiRouter.use('/exceptions', exceptionRoutes);
apiRouter.use('/analytics', analyticsRoutes);
apiRouter.use('/ai', aiRoutes);
apiRouter.use('/email', emailRoutes);
apiRouter.use('/responses', responseRoutes);
apiRouter.use('/supply-chain', supplyChainRoutes);

app.use('/api/v1', apiRouter);

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    error: 'Route not found',
    message: `Cannot ${req.method} ${req.originalUrl}`,
  });
});

// Global error handler
app.use(errorHandler);

// Start server
const PORT = config.port || 3001;

app.listen(PORT, () => {
  console.log(`🚀 Server running on port ${PORT}`);
  console.log(`📊 Environment: ${config.nodeEnv}`);
  console.log(`🔗 API Base URL: http://localhost:${PORT}/api/v1`);
  console.log(`🏥 Health Check: http://localhost:${PORT}/health`);
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('SIGTERM received, shutting down gracefully');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('SIGINT received, shutting down gracefully');
  process.exit(0);
});

export default app;