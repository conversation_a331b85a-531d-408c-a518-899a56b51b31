import { z } from 'zod';
import { Priority, InvoiceStatus, PaymentMethod, ShipmentStatus, RuleDomain, ExceptionType, Severity, ExceptionStatus } from '@ar-scia/shared-types';
export declare const userSchema: z.ZodObject<{
    email: z.ZodString;
    firstName: z.ZodString;
    lastName: z.ZodString;
    password: z.ZodOptional<z.ZodString>;
    isActive: z.ZodDefault<z.ZodBoolean>;
}, "strip", z.ZodTypeAny, {
    email: string;
    firstName: string;
    lastName: string;
    isActive: boolean;
    password?: string | undefined;
}, {
    email: string;
    firstName: string;
    lastName: string;
    password?: string | undefined;
    isActive?: boolean | undefined;
}>;
export declare const customerSchema: z.ZodObject<{
    name: z.ZodString;
    email: z.ZodOptional<z.ZodString>;
    phone: z.ZodOptional<z.ZodString>;
    address: z.<PERSON>odOptional<z.ZodString>;
    priority: z.<PERSON>od<PERSON>efault<z.ZodNativeEnum<typeof Priority>>;
    isActive: z.ZodDefault<z.ZodBoolean>;
}, "strip", z.ZodTypeAny, {
    isActive: boolean;
    name: string;
    priority: Priority;
    email?: string | undefined;
    phone?: string | undefined;
    address?: string | undefined;
}, {
    name: string;
    email?: string | undefined;
    isActive?: boolean | undefined;
    phone?: string | undefined;
    address?: string | undefined;
    priority?: Priority | undefined;
}>;
export declare const invoiceSchema: z.ZodObject<{
    invoiceNumber: z.ZodString;
    customerId: z.ZodString;
    amount: z.ZodNumber;
    dueDate: z.ZodDate;
    status: z.ZodDefault<z.ZodNativeEnum<typeof InvoiceStatus>>;
    xeroId: z.ZodOptional<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    status: InvoiceStatus;
    invoiceNumber: string;
    customerId: string;
    amount: number;
    dueDate: Date;
    xeroId?: string | undefined;
}, {
    invoiceNumber: string;
    customerId: string;
    amount: number;
    dueDate: Date;
    status?: InvoiceStatus | undefined;
    xeroId?: string | undefined;
}>;
export declare const paymentSchema: z.ZodObject<{
    invoiceId: z.ZodString;
    amount: z.ZodNumber;
    paymentDate: z.ZodDate;
    method: z.ZodNativeEnum<typeof PaymentMethod>;
    reference: z.ZodOptional<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    amount: number;
    invoiceId: string;
    paymentDate: Date;
    method: PaymentMethod;
    reference?: string | undefined;
}, {
    amount: number;
    invoiceId: string;
    paymentDate: Date;
    method: PaymentMethod;
    reference?: string | undefined;
}>;
export declare const shipmentSchema: z.ZodObject<{
    shipmentNumber: z.ZodString;
    customerId: z.ZodString;
    containerNumber: z.ZodOptional<z.ZodString>;
    status: z.ZodDefault<z.ZodNativeEnum<typeof ShipmentStatus>>;
    origin: z.ZodOptional<z.ZodString>;
    destination: z.ZodOptional<z.ZodString>;
    estimatedDate: z.ZodOptional<z.ZodDate>;
    actualDate: z.ZodOptional<z.ZodDate>;
}, "strip", z.ZodTypeAny, {
    status: ShipmentStatus;
    customerId: string;
    shipmentNumber: string;
    containerNumber?: string | undefined;
    origin?: string | undefined;
    destination?: string | undefined;
    estimatedDate?: Date | undefined;
    actualDate?: Date | undefined;
}, {
    customerId: string;
    shipmentNumber: string;
    status?: ShipmentStatus | undefined;
    containerNumber?: string | undefined;
    origin?: string | undefined;
    destination?: string | undefined;
    estimatedDate?: Date | undefined;
    actualDate?: Date | undefined;
}>;
export declare const businessRuleSchema: z.ZodObject<{
    name: z.ZodString;
    description: z.ZodOptional<z.ZodString>;
    domain: z.ZodNativeEnum<typeof RuleDomain>;
    conditions: z.ZodAny;
    actions: z.ZodAny;
    isActive: z.ZodDefault<z.ZodBoolean>;
    priority: z.ZodDefault<z.ZodNumber>;
}, "strip", z.ZodTypeAny, {
    isActive: boolean;
    name: string;
    priority: number;
    domain: RuleDomain;
    description?: string | undefined;
    conditions?: any;
    actions?: any;
}, {
    name: string;
    domain: RuleDomain;
    isActive?: boolean | undefined;
    priority?: number | undefined;
    description?: string | undefined;
    conditions?: any;
    actions?: any;
}>;
export declare const exceptionSchema: z.ZodObject<{
    type: z.ZodNativeEnum<typeof ExceptionType>;
    severity: z.ZodNativeEnum<typeof Severity>;
    title: z.ZodString;
    description: z.ZodString;
    status: z.ZodDefault<z.ZodNativeEnum<typeof ExceptionStatus>>;
    ruleId: z.ZodOptional<z.ZodString>;
    shipmentId: z.ZodOptional<z.ZodString>;
    customerId: z.ZodOptional<z.ZodString>;
    data: z.ZodOptional<z.ZodAny>;
}, "strip", z.ZodTypeAny, {
    type: ExceptionType;
    status: ExceptionStatus;
    description: string;
    severity: Severity;
    title: string;
    customerId?: string | undefined;
    ruleId?: string | undefined;
    shipmentId?: string | undefined;
    data?: any;
}, {
    type: ExceptionType;
    description: string;
    severity: Severity;
    title: string;
    status?: ExceptionStatus | undefined;
    customerId?: string | undefined;
    ruleId?: string | undefined;
    shipmentId?: string | undefined;
    data?: any;
}>;
export declare const formatCurrency: (amount: number, currency?: string) => string;
export declare const formatDate: (date: Date, format?: string) => string;
export declare const formatDateTime: (date: Date) => string;
export declare const calculateDaysBetween: (startDate: Date, endDate: Date) => number;
export declare const isOverdue: (dueDate: Date) => boolean;
export declare const getDaysOverdue: (dueDate: Date) => number;
export declare const getAgingBucket: (dueDate: Date) => string;
export declare const generateId: () => string;
export declare const slugify: (text: string) => string;
export declare const truncateText: (text: string, maxLength: number) => string;
export declare const capitalizeFirst: (text: string) => string;
export declare const formatPhoneNumber: (phone: string) => string;
export declare const validateEmail: (email: string) => boolean;
export declare const sanitizeInput: (input: string) => string;
export declare const hasPermission: (userPermissions: string[], requiredPermission: string) => boolean;
export declare const extractPermissions: (roles: any[]) => string[];
export declare const transformPaginationParams: (params: any) => {
    page: number;
    limit: number;
    sortBy: any;
    sortOrder: string;
    search: any;
};
export declare const createPaginationInfo: (page: number, limit: number, total: number) => {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
};
export declare class AppError extends Error {
    statusCode: number;
    isOperational: boolean;
    constructor(message: string, statusCode?: number, isOperational?: boolean);
}
export declare const createApiResponse: <T>(success: boolean, data?: T, error?: string, message?: string, pagination?: any) => {
    success: boolean;
    data: T | undefined;
    error: string | undefined;
    message: string | undefined;
    pagination: any;
};
export declare const DEFAULT_PAGE_SIZE = 10;
export declare const MAX_PAGE_SIZE = 100;
export declare const JWT_EXPIRES_IN = "24h";
export declare const PASSWORD_MIN_LENGTH = 8;
export declare const UPLOAD_MAX_SIZE: number;
export declare const ROLE_PERMISSIONS: {
    SUPER_ADMIN: string[];
    FINANCE_MANAGER: string[];
    SUPPLY_CHAIN_MANAGER: string[];
    OPERATIONS_STAFF: string[];
    CUSTOMER_SERVICE: string[];
};
