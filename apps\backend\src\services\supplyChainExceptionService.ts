import { AIService } from './aiService';
import { AppError } from '@ar-scia/shared-utils';

export interface Shipment {
  id: string;
  orderNumber: string;
  customerId: string;
  customerName: string;
  containerNumber?: string;
  vesselName?: string;
  portOfLoading: string;
  portOfDischarge: string;
  estimatedDeparture: Date;
  estimatedArrival: Date;
  actualDeparture?: Date;
  actualArrival?: Date;
  status: 'planned' | 'in_transit' | 'delayed' | 'arrived' | 'delivered' | 'exception';
  priority: 'low' | 'medium' | 'high' | 'critical';
  value: number;
  items: ShipmentItem[];
  documents: ShipmentDocument[];
  milestones: ShipmentMilestone[];
}

export interface ShipmentItem {
  id: string;
  sku: string;
  description: string;
  quantity: number;
  unitValue: number;
  weight: number;
  dimensions: {
    length: number;
    width: number;
    height: number;
  };
  hazardous: boolean;
  temperatureControlled: boolean;
}

export interface ShipmentDocument {
  id: string;
  type: 'bill_of_lading' | 'commercial_invoice' | 'packing_list' | 'certificate_of_origin' | 'customs_declaration' | 'insurance_certificate';
  name: string;
  required: boolean;
  received: boolean;
  receivedDate?: Date;
  expiryDate?: Date;
  issuer?: string;
  documentNumber?: string;
}

export interface ShipmentMilestone {
  id: string;
  type: 'booking_confirmed' | 'cargo_loaded' | 'vessel_departed' | 'in_transit' | 'customs_cleared' | 'delivered';
  expectedDate: Date;
  actualDate?: Date;
  location: string;
  status: 'pending' | 'completed' | 'delayed' | 'exception';
  notes?: string;
}

export interface SupplyChainException {
  id: string;
  shipmentId: string;
  type: 'delay' | 'missing_document' | 'customs_hold' | 'weather_delay' | 'port_congestion' | 'vessel_breakdown' | 'documentation_error' | 'quality_issue';
  severity: 'low' | 'medium' | 'high' | 'critical';
  title: string;
  description: string;
  detectedDate: Date;
  expectedResolutionDate?: Date;
  actualResolutionDate?: Date;
  status: 'open' | 'investigating' | 'resolved' | 'escalated';
  impact: {
    delayDays: number;
    costImpact: number;
    customerImpact: 'none' | 'low' | 'medium' | 'high';
    revenueAtRisk: number;
  };
  rootCause?: string;
  resolution?: string;
  preventiveActions: string[];
  assignedTo?: string;
  aiConfidence: number;
  relatedExceptions: string[];
}

export interface ExceptionRule {
  id: string;
  name: string;
  description: string;
  type: 'delay' | 'document' | 'customs' | 'quality' | 'cost';
  conditions: RuleCondition[];
  actions: RuleAction[];
  priority: number;
  enabled: boolean;
  aiEnhanced: boolean;
}

export interface RuleCondition {
  field: string;
  operator: 'equals' | 'not_equals' | 'greater_than' | 'less_than' | 'contains' | 'missing' | 'expired';
  value: any;
  logicalOperator?: 'AND' | 'OR';
}

export interface RuleAction {
  type: 'create_exception' | 'send_notification' | 'escalate' | 'update_status' | 'schedule_followup';
  parameters: Record<string, any>;
}

export class SupplyChainExceptionService {
  private aiService: AIService;

  constructor() {
    this.aiService = new AIService();
  }

  async detectExceptions(shipment: Shipment): Promise<SupplyChainException[]> {
    try {
      const exceptions: SupplyChainException[] = [];

      // Rule-based detection
      const ruleBasedExceptions = await this.detectRuleBasedExceptions(shipment);
      exceptions.push(...ruleBasedExceptions);

      // AI-enhanced detection
      const aiExceptions = await this.detectAIExceptions(shipment);
      exceptions.push(...aiExceptions);

      // Deduplicate and prioritize
      const uniqueExceptions = this.deduplicateExceptions(exceptions);
      
      return uniqueExceptions.sort((a, b) => {
        const severityOrder = { critical: 4, high: 3, medium: 2, low: 1 };
        return severityOrder[b.severity] - severityOrder[a.severity];
      });
    } catch (error) {
      console.error('Exception detection error:', error);
      throw new AppError('Failed to detect supply chain exceptions', 500);
    }
  }

  private async detectRuleBasedExceptions(shipment: Shipment): Promise<SupplyChainException[]> {
    const exceptions: SupplyChainException[] = [];
    const now = new Date();

    // Delay detection
    if (shipment.estimatedArrival < now && !shipment.actualArrival) {
      const delayDays = Math.ceil((now.getTime() - shipment.estimatedArrival.getTime()) / (1000 * 60 * 60 * 24));
      
      exceptions.push({
        id: `exc_${Date.now()}_delay`,
        shipmentId: shipment.id,
        type: 'delay',
        severity: delayDays > 7 ? 'critical' : delayDays > 3 ? 'high' : 'medium',
        title: `Shipment Delay - ${delayDays} days overdue`,
        description: `Shipment ${shipment.orderNumber} is ${delayDays} days past estimated arrival date`,
        detectedDate: now,
        status: 'open',
        impact: {
          delayDays,
          costImpact: delayDays * 500, // Estimated daily cost
          customerImpact: delayDays > 7 ? 'high' : delayDays > 3 ? 'medium' : 'low',
          revenueAtRisk: shipment.value * (delayDays > 7 ? 0.1 : 0.05)
        },
        preventiveActions: [],
        aiConfidence: 0.95,
        relatedExceptions: []
      });
    }

    // Missing document detection
    const missingDocs = shipment.documents.filter(doc => doc.required && !doc.received);
    if (missingDocs.length > 0) {
      exceptions.push({
        id: `exc_${Date.now()}_docs`,
        shipmentId: shipment.id,
        type: 'missing_document',
        severity: missingDocs.some(d => d.type === 'bill_of_lading') ? 'critical' : 'high',
        title: `Missing Required Documents (${missingDocs.length})`,
        description: `Missing documents: ${missingDocs.map(d => d.name).join(', ')}`,
        detectedDate: now,
        status: 'open',
        impact: {
          delayDays: 2,
          costImpact: 1000,
          customerImpact: 'medium',
          revenueAtRisk: 0
        },
        preventiveActions: [],
        aiConfidence: 1.0,
        relatedExceptions: []
      });
    }

    // Expired document detection
    const expiredDocs = shipment.documents.filter(doc => 
      doc.expiryDate && doc.expiryDate < now && doc.received
    );
    if (expiredDocs.length > 0) {
      exceptions.push({
        id: `exc_${Date.now()}_expired`,
        shipmentId: shipment.id,
        type: 'documentation_error',
        severity: 'high',
        title: `Expired Documents (${expiredDocs.length})`,
        description: `Expired documents: ${expiredDocs.map(d => d.name).join(', ')}`,
        detectedDate: now,
        status: 'open',
        impact: {
          delayDays: 3,
          costImpact: 1500,
          customerImpact: 'high',
          revenueAtRisk: shipment.value * 0.02
        },
        preventiveActions: [],
        aiConfidence: 1.0,
        relatedExceptions: []
      });
    }

    return exceptions;
  }

  private async detectAIExceptions(shipment: Shipment): Promise<SupplyChainException[]> {
    try {
      const prompt = `
        Analyze this shipment data for potential supply chain exceptions:
        
        Shipment: ${JSON.stringify(shipment, null, 2)}
        
        Look for:
        1. Unusual patterns in timing or routing
        2. Risk factors based on destination/origin
        3. Potential customs or regulatory issues
        4. Weather or seasonal impact risks
        5. Container/vessel capacity issues
        6. Customer priority conflicts
        
        Return analysis in this JSON format:
        {
          "exceptions": [
            {
              "type": "delay|missing_document|customs_hold|weather_delay|port_congestion|vessel_breakdown|documentation_error|quality_issue",
              "severity": "low|medium|high|critical",
              "title": "Exception title",
              "description": "Detailed description",
              "confidence": 0.85,
              "predictedImpact": {
                "delayDays": 2,
                "costImpact": 1000,
                "customerImpact": "low|medium|high",
                "revenueAtRisk": 5000
              },
              "rootCause": "Identified root cause",
              "preventiveActions": ["action1", "action2"]
            }
          ]
        }
      `;

      const result = await this.aiService.processNaturalLanguageQuery(prompt);
      const jsonMatch = result.match(/\{[\s\S]*\}/);
      
      if (!jsonMatch) {
        return [];
      }

      const analysis = JSON.parse(jsonMatch[0]);
      const exceptions: SupplyChainException[] = [];

      for (const exc of analysis.exceptions || []) {
        if (exc.confidence > 0.7) { // Only include high-confidence AI predictions
          exceptions.push({
            id: `exc_ai_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            shipmentId: shipment.id,
            type: exc.type,
            severity: exc.severity,
            title: exc.title,
            description: exc.description,
            detectedDate: new Date(),
            status: 'open',
            impact: {
              delayDays: exc.predictedImpact?.delayDays || 0,
              costImpact: exc.predictedImpact?.costImpact || 0,
              customerImpact: exc.predictedImpact?.customerImpact || 'low',
              revenueAtRisk: exc.predictedImpact?.revenueAtRisk || 0
            },
            rootCause: exc.rootCause,
            preventiveActions: exc.preventiveActions || [],
            aiConfidence: exc.confidence,
            relatedExceptions: []
          });
        }
      }

      return exceptions;
    } catch (error) {
      console.error('AI exception detection error:', error);
      return [];
    }
  }

  private deduplicateExceptions(exceptions: SupplyChainException[]): SupplyChainException[] {
    const seen = new Set<string>();
    const unique: SupplyChainException[] = [];

    for (const exception of exceptions) {
      const key = `${exception.shipmentId}_${exception.type}_${exception.title}`;
      if (!seen.has(key)) {
        seen.add(key);
        unique.push(exception);
      }
    }

    return unique;
  }

  async getExceptionsByShipment(shipmentId: string): Promise<SupplyChainException[]> {
    // In a real implementation, fetch from database
    console.log(`Fetching exceptions for shipment: ${shipmentId}`);
    return [];
  }

  async getExceptionsByCustomer(customerId: string): Promise<SupplyChainException[]> {
    // In a real implementation, fetch from database
    console.log(`Fetching exceptions for customer: ${customerId}`);
    return [];
  }

  async updateExceptionStatus(
    exceptionId: string, 
    status: SupplyChainException['status'],
    resolution?: string,
    assignedTo?: string
  ): Promise<SupplyChainException> {
    // In a real implementation, update in database
    console.log(`Updating exception ${exceptionId} to status: ${status}`);
    throw new AppError('Not implemented', 501);
  }

  async createManualException(
    shipmentId: string,
    exceptionData: Partial<SupplyChainException>
  ): Promise<SupplyChainException> {
    const exception: SupplyChainException = {
      id: `exc_manual_${Date.now()}`,
      shipmentId,
      type: exceptionData.type || 'quality_issue',
      severity: exceptionData.severity || 'medium',
      title: exceptionData.title || 'Manual Exception',
      description: exceptionData.description || '',
      detectedDate: new Date(),
      status: 'open',
      impact: exceptionData.impact || {
        delayDays: 0,
        costImpact: 0,
        customerImpact: 'low',
        revenueAtRisk: 0
      },
      preventiveActions: exceptionData.preventiveActions || [],
      aiConfidence: 0, // Manual exceptions have no AI confidence
      relatedExceptions: [],
      ...exceptionData
    };

    // In a real implementation, save to database
    console.log('Created manual exception:', exception);
    return exception;
  }

  async getExceptionMetrics(dateRange?: { start: Date; end: Date }): Promise<{
    totalExceptions: number;
    byType: Record<string, number>;
    bySeverity: Record<string, number>;
    byStatus: Record<string, number>;
    averageResolutionTime: number;
    totalCostImpact: number;
    totalRevenueAtRisk: number;
  }> {
    // In a real implementation, calculate from database
    return {
      totalExceptions: 45,
      byType: {
        delay: 18,
        missing_document: 12,
        customs_hold: 8,
        weather_delay: 4,
        port_congestion: 3
      },
      bySeverity: {
        critical: 5,
        high: 15,
        medium: 20,
        low: 5
      },
      byStatus: {
        open: 25,
        investigating: 12,
        resolved: 8
      },
      averageResolutionTime: 3.2,
      totalCostImpact: 125000,
      totalRevenueAtRisk: 450000
    };
  }

  // AI-powered exception prediction
  async predictPotentialExceptions(shipment: Shipment): Promise<{
    predictions: Array<{
      type: string;
      probability: number;
      timeframe: string;
      preventiveActions: string[];
    }>;
    riskScore: number;
  }> {
    try {
      const prompt = `
        Predict potential future exceptions for this shipment:
        
        ${JSON.stringify(shipment, null, 2)}
        
        Consider:
        1. Historical patterns for similar routes
        2. Seasonal factors
        3. Current global supply chain conditions
        4. Customer priority and requirements
        5. Document complexity and requirements
        
        Return predictions in JSON format:
        {
          "predictions": [
            {
              "type": "exception_type",
              "probability": 0.75,
              "timeframe": "next 3 days",
              "preventiveActions": ["action1", "action2"]
            }
          ],
          "riskScore": 0.65
        }
      `;

      const result = await this.aiService.processNaturalLanguageQuery(prompt);
      const jsonMatch = result.match(/\{[\s\S]*\}/);
      
      if (!jsonMatch) {
        return { predictions: [], riskScore: 0.5 };
      }

      return JSON.parse(jsonMatch[0]);
    } catch (error) {
      console.error('Exception prediction error:', error);
      return { predictions: [], riskScore: 0.5 };
    }
  }

  // Generate exception resolution recommendations
  async generateResolutionRecommendations(exception: SupplyChainException): Promise<{
    recommendations: Array<{
      action: string;
      priority: number;
      estimatedCost: number;
      estimatedTime: string;
      successProbability: number;
    }>;
    alternativeOptions: string[];
  }> {
    try {
      const prompt = `
        Generate resolution recommendations for this supply chain exception:
        
        ${JSON.stringify(exception, null, 2)}
        
        Provide:
        1. Specific actionable recommendations
        2. Cost and time estimates
        3. Success probability for each option
        4. Alternative approaches
        
        Return in JSON format:
        {
          "recommendations": [
            {
              "action": "Specific action to take",
              "priority": 1,
              "estimatedCost": 1000,
              "estimatedTime": "2-3 days",
              "successProbability": 0.85
            }
          ],
          "alternativeOptions": ["option1", "option2"]
        }
      `;

      const result = await this.aiService.processNaturalLanguageQuery(prompt);
      const jsonMatch = result.match(/\{[\s\S]*\}/);
      
      if (!jsonMatch) {
        return { recommendations: [], alternativeOptions: [] };
      }

      return JSON.parse(jsonMatch[0]);
    } catch (error) {
      console.error('Resolution recommendation error:', error);
      return { recommendations: [], alternativeOptions: [] };
    }
  }
}

export const supplyChainExceptionService = new SupplyChainExceptionService();

// Supply chain exception routes will be added separately
