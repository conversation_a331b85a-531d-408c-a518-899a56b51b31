"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.InvoiceService = void 0;
const client_1 = require("@prisma/client");
const shared_utils_1 = require("@ar-scia/shared-utils");
const prisma = new client_1.PrismaClient();
class InvoiceService {
    async getInvoices(params) {
        const { page, limit, sortBy, sortOrder, search } = (0, shared_utils_1.transformPaginationParams)(params);
        const { status, customerId, dueDateFrom, dueDateTo, amountMin, amountMax } = params;
        const where = {};
        if (search) {
            where.OR = [
                { invoiceNumber: { contains: search, mode: 'insensitive' } },
                { customer: { name: { contains: search, mode: 'insensitive' } } }
            ];
        }
        if (status && status.length > 0) {
            where.status = { in: status };
        }
        if (customerId) {
            where.customerId = customerId;
        }
        if (dueDateFrom || dueDateTo) {
            where.dueDate = {};
            if (dueDateFrom)
                where.dueDate.gte = new Date(dueDateFrom);
            if (dueDateTo)
                where.dueDate.lte = new Date(dueDateTo);
        }
        if (amountMin !== undefined || amountMax !== undefined) {
            where.amount = {};
            if (amountMin !== undefined)
                where.amount.gte = amountMin;
            if (amountMax !== undefined)
                where.amount.lte = amountMax;
        }
        const total = await prisma.invoice.count({ where });
        const invoices = await prisma.invoice.findMany({
            where,
            include: {
                customer: {
                    select: {
                        id: true,
                        name: true,
                        email: true,
                        priority: true
                    }
                },
                payments: {
                    select: {
                        id: true,
                        amount: true,
                        paymentDate: true,
                        method: true
                    }
                },
                communications: {
                    select: {
                        id: true,
                        type: true,
                        direction: true,
                        sentAt: true
                    },
                    orderBy: { createdAt: 'desc' },
                    take: 3
                }
            },
            orderBy: { [sortBy]: sortOrder },
            skip: (page - 1) * limit,
            take: limit
        });
        const invoicesWithMetrics = invoices.map(invoice => {
            const totalPaid = invoice.payments.reduce((sum, payment) => sum + Number(payment.amount), 0);
            const remainingAmount = Number(invoice.amount) - totalPaid;
            const isOverdue = invoice.status === 'OVERDUE' || (invoice.status === 'PENDING' && new Date() > invoice.dueDate);
            const daysOverdue = isOverdue ? Math.floor((new Date().getTime() - invoice.dueDate.getTime()) / (1000 * 60 * 60 * 24)) : 0;
            const agingBucket = (0, shared_utils_1.getAgingBucket)(invoice.dueDate);
            return {
                ...invoice,
                metrics: {
                    totalPaid,
                    remainingAmount,
                    isOverdue,
                    daysOverdue,
                    agingBucket,
                    paymentCount: invoice.payments.length,
                    lastCommunication: invoice.communications[0]?.sentAt || null
                }
            };
        });
        const pagination = (0, shared_utils_1.createPaginationInfo)(page, limit, total);
        return {
            invoices: invoicesWithMetrics,
            pagination
        };
    }
}
exports.InvoiceService = InvoiceService;
//# sourceMappingURL=invoiceService.js.map