import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

async function seedDatabase() {
  console.log('🌱 Starting database seeding...');

  try {
    // Create permissions
    console.log('📋 Creating permissions...');
    const permissions = [
      // Customer permissions
      { name: 'customers:read', resource: 'customers', action: 'read' },
      { name: 'customers:write', resource: 'customers', action: 'write' },
      { name: 'customers:delete', resource: 'customers', action: 'delete' },
      
      // Invoice permissions
      { name: 'invoices:read', resource: 'invoices', action: 'read' },
      { name: 'invoices:write', resource: 'invoices', action: 'write' },
      { name: 'invoices:delete', resource: 'invoices', action: 'delete' },
      { name: 'invoices:send', resource: 'invoices', action: 'send' },
      
      // Payment permissions
      { name: 'payments:read', resource: 'payments', action: 'read' },
      { name: 'payments:write', resource: 'payments', action: 'write' },
      { name: 'payments:delete', resource: 'payments', action: 'delete' },
      
      // Shipment permissions
      { name: 'shipments:read', resource: 'shipments', action: 'read' },
      { name: 'shipments:write', resource: 'shipments', action: 'write' },
      { name: 'shipments:delete', resource: 'shipments', action: 'delete' },
      { name: 'shipments:track', resource: 'shipments', action: 'track' },
      
      // Analytics permissions
      { name: 'analytics:read', resource: 'analytics', action: 'read' },
      { name: 'analytics:export', resource: 'analytics', action: 'export' },
      
      // User management permissions
      { name: 'users:read', resource: 'users', action: 'read' },
      { name: 'users:write', resource: 'users', action: 'write' },
      { name: 'users:delete', resource: 'users', action: 'delete' },
      
      // Role management permissions
      { name: 'roles:read', resource: 'roles', action: 'read' },
      { name: 'roles:write', resource: 'roles', action: 'write' },
      { name: 'roles:delete', resource: 'roles', action: 'delete' },
      
      // Business rules permissions
      { name: 'rules:read', resource: 'rules', action: 'read' },
      { name: 'rules:write', resource: 'rules', action: 'write' },
      { name: 'rules:delete', resource: 'rules', action: 'delete' },
      
      // System permissions
      { name: 'system:admin', resource: 'system', action: 'admin' },
      { name: 'system:audit', resource: 'system', action: 'audit' }
    ];

    for (const permission of permissions) {
      await prisma.permission.upsert({
        where: { name: permission.name },
        update: {},
        create: permission
      });
    }

    // Create roles
    console.log('👥 Creating roles...');
    
    // Super Admin Role
    const superAdminRole = await prisma.role.upsert({
      where: { name: 'SUPER_ADMIN' },
      update: {},
      create: {
        name: 'SUPER_ADMIN',
        description: 'Full system access with all permissions'
      }
    });

    // Admin Role
    const adminRole = await prisma.role.upsert({
      where: { name: 'ADMIN' },
      update: {},
      create: {
        name: 'ADMIN',
        description: 'Administrative access to most system features'
      }
    });

    // Manager Role
    const managerRole = await prisma.role.upsert({
      where: { name: 'MANAGER' },
      update: {},
      create: {
        name: 'MANAGER',
        description: 'Management access to business operations'
      }
    });

    // User Role
    const userRole = await prisma.role.upsert({
      where: { name: 'USER' },
      update: {},
      create: {
        name: 'USER',
        description: 'Standard user access to basic features'
      }
    });

    // Viewer Role
    const viewerRole = await prisma.role.upsert({
      where: { name: 'VIEWER' },
      update: {},
      create: {
        name: 'VIEWER',
        description: 'Read-only access to system data'
      }
    });

    // Assign permissions to roles
    console.log('🔗 Assigning permissions to roles...');
    
    // Super Admin gets all permissions
    const allPermissions = await prisma.permission.findMany();
    for (const permission of allPermissions) {
      await prisma.rolePermission.upsert({
        where: {
          roleId_permissionId: {
            roleId: superAdminRole.id,
            permissionId: permission.id
          }
        },
        update: {},
        create: {
          roleId: superAdminRole.id,
          permissionId: permission.id
        }
      });
    }

    // Admin permissions (all except system admin)
    const adminPermissions = allPermissions.filter(p => p.name !== 'system:admin');
    for (const permission of adminPermissions) {
      await prisma.rolePermission.upsert({
        where: {
          roleId_permissionId: {
            roleId: adminRole.id,
            permissionId: permission.id
          }
        },
        update: {},
        create: {
          roleId: adminRole.id,
          permissionId: permission.id
        }
      });
    }

    // Manager permissions (business operations)
    const managerPermissionNames = [
      'customers:read', 'customers:write',
      'invoices:read', 'invoices:write', 'invoices:send',
      'payments:read', 'payments:write',
      'shipments:read', 'shipments:write', 'shipments:track',
      'analytics:read', 'analytics:export',
      'rules:read', 'rules:write'
    ];
    const managerPermissions = allPermissions.filter(p => managerPermissionNames.includes(p.name));
    for (const permission of managerPermissions) {
      await prisma.rolePermission.upsert({
        where: {
          roleId_permissionId: {
            roleId: managerRole.id,
            permissionId: permission.id
          }
        },
        update: {},
        create: {
          roleId: managerRole.id,
          permissionId: permission.id
        }
      });
    }

    // User permissions (basic operations)
    const userPermissionNames = [
      'customers:read',
      'invoices:read', 'invoices:write',
      'payments:read',
      'shipments:read', 'shipments:track',
      'analytics:read'
    ];
    const userPermissions = allPermissions.filter(p => userPermissionNames.includes(p.name));
    for (const permission of userPermissions) {
      await prisma.rolePermission.upsert({
        where: {
          roleId_permissionId: {
            roleId: userRole.id,
            permissionId: permission.id
          }
        },
        update: {},
        create: {
          roleId: userRole.id,
          permissionId: permission.id
        }
      });
    }

    // Viewer permissions (read-only)
    const viewerPermissionNames = [
      'customers:read',
      'invoices:read',
      'payments:read',
      'shipments:read',
      'analytics:read'
    ];
    const viewerPermissions = allPermissions.filter(p => viewerPermissionNames.includes(p.name));
    for (const permission of viewerPermissions) {
      await prisma.rolePermission.upsert({
        where: {
          roleId_permissionId: {
            roleId: viewerRole.id,
            permissionId: permission.id
          }
        },
        update: {},
        create: {
          roleId: viewerRole.id,
          permissionId: permission.id
        }
      });
    }

    // Create default admin user
    console.log('👤 Creating default admin user...');
    const hashedPassword = await bcrypt.hash('Admin123!', 12);
    
    const adminUser = await prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {},
      create: {
        email: '<EMAIL>',
        password: hashedPassword,
        firstName: 'System',
        lastName: 'Administrator',
        isActive: true
      }
    });

    // Assign super admin role to default user
    await prisma.userRole.upsert({
      where: {
        userId_roleId: {
          userId: adminUser.id,
          roleId: superAdminRole.id
        }
      },
      update: {},
      create: {
        userId: adminUser.id,
        roleId: superAdminRole.id
      }
    });

    console.log('✅ Database seeding completed successfully!');
    console.log('📧 Default admin user: <EMAIL>');
    console.log('🔑 Default admin password: Admin123!');
    
  } catch (error) {
    console.error('❌ Error seeding database:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run seeder if called directly
if (require.main === module) {
  seedDatabase()
    .catch((error) => {
      console.error(error);
      process.exit(1);
    });
}

export default seedDatabase;
