{"version": 3, "file": "auth.js", "sourceRoot": "", "sources": ["../../src/middleware/auth.ts"], "names": [], "mappings": ";;;;;;AACA,gEAA+B;AAC/B,2CAA8C;AAC9C,wDAAoF;AAEpF,4CAAyC;AAEzC,MAAM,MAAM,GAAG,IAAI,qBAAY,EAAE,CAAC;AAkB3B,MAAM,iBAAiB,GAAG,KAAK,EACpC,GAAY,EACZ,GAAa,EACb,IAAkB,EAClB,EAAE;IACF,IAAI,CAAC;QACH,MAAM,UAAU,GAAG,GAAG,CAAC,OAAO,CAAC,aAAa,CAAC;QAC7C,MAAM,KAAK,GAAG,UAAU,IAAI,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAErD,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,MAAM,IAAI,uBAAQ,CAAC,uBAAuB,EAAE,GAAG,CAAC,CAAC;QACnD,CAAC;QAGD,MAAM,OAAO,GAAG,sBAAG,CAAC,MAAM,CAAC,KAAK,EAAE,eAAM,CAAC,SAAS,CAAe,CAAC;QAGlE,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;YACxC,KAAK,EAAE;gBACL,EAAE,EAAE,OAAO,CAAC,MAAM;gBAClB,QAAQ,EAAE,IAAI;aACf;YACD,OAAO,EAAE;gBACP,KAAK,EAAE;oBACL,OAAO,EAAE;wBACP,IAAI,EAAE;4BACJ,OAAO,EAAE;gCACP,WAAW,EAAE;oCACX,OAAO,EAAE;wCACP,UAAU,EAAE,IAAI;qCACjB;iCACF;6BACF;yBACF;qBACF;iBACF;aACF;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,MAAM,IAAI,uBAAQ,CAAC,4BAA4B,EAAE,GAAG,CAAC,CAAC;QACxD,CAAC;QAGD,MAAM,WAAW,GAAG,IAAA,iCAAkB,EACpC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YACpB,WAAW,EAAE,EAAE,CAAC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,UAAU,CAAC;SAC1D,CAAC,CAAC,CACJ,CAAC;QAGF,GAAG,CAAC,IAAI,GAAG;YACT,EAAE,EAAE,IAAI,CAAC,EAAE;YACX,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC;YACzC,WAAW;SACZ,CAAC;QAEF,IAAI,EAAE,CAAC;IACT,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,IAAI,KAAK,YAAY,sBAAG,CAAC,iBAAiB,EAAE,CAAC;YAC3C,OAAO,IAAI,CAAC,IAAI,uBAAQ,CAAC,eAAe,EAAE,GAAG,CAAC,CAAC,CAAC;QAClD,CAAC;QACD,IAAI,KAAK,YAAY,sBAAG,CAAC,iBAAiB,EAAE,CAAC;YAC3C,OAAO,IAAI,CAAC,IAAI,uBAAQ,CAAC,eAAe,EAAE,GAAG,CAAC,CAAC,CAAC;QAClD,CAAC;QACD,IAAI,CAAC,KAAK,CAAC,CAAC;IACd,CAAC;AACH,CAAC,CAAC;AAtEW,QAAA,iBAAiB,qBAsE5B;AAEK,MAAM,iBAAiB,GAAG,CAAC,UAAkB,EAAE,EAAE;IACtD,OAAO,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;QACzD,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;YACd,OAAO,IAAI,CAAC,IAAI,uBAAQ,CAAC,yBAAyB,EAAE,GAAG,CAAC,CAAC,CAAC;QAC5D,CAAC;QAED,IAAI,CAAC,IAAA,4BAAa,EAAC,GAAG,CAAC,IAAI,CAAC,WAAW,EAAE,UAAU,CAAC,EAAE,CAAC;YACrD,OAAO,IAAI,CAAC,IAAI,uBAAQ,CAAC,0BAA0B,EAAE,GAAG,CAAC,CAAC,CAAC;QAC7D,CAAC;QAED,IAAI,EAAE,CAAC;IACT,CAAC,CAAC;AACJ,CAAC,CAAC;AAZW,QAAA,iBAAiB,qBAY5B;AAEK,MAAM,WAAW,GAAG,CAAC,IAAY,EAAE,EAAE;IAC1C,OAAO,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;QACzD,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;YACd,OAAO,IAAI,CAAC,IAAI,uBAAQ,CAAC,yBAAyB,EAAE,GAAG,CAAC,CAAC,CAAC;QAC5D,CAAC;QAED,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,aAAa,CAAC,EAAE,CAAC;YAC9E,OAAO,IAAI,CAAC,IAAI,uBAAQ,CAAC,+BAA+B,EAAE,GAAG,CAAC,CAAC,CAAC;QAClE,CAAC;QAED,IAAI,EAAE,CAAC;IACT,CAAC,CAAC;AACJ,CAAC,CAAC;AAZW,QAAA,WAAW,eAYtB;AAEK,MAAM,cAAc,GAAG,CAAC,KAAe,EAAE,EAAE;IAChD,OAAO,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;QACzD,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;YACd,OAAO,IAAI,CAAC,IAAI,uBAAQ,CAAC,yBAAyB,EAAE,GAAG,CAAC,CAAC,CAAC;QAC5D,CAAC;QAED,MAAM,eAAe,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,IAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;YACnD,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC;QAE9D,IAAI,CAAC,eAAe,EAAE,CAAC;YACrB,OAAO,IAAI,CAAC,IAAI,uBAAQ,CAAC,+BAA+B,EAAE,GAAG,CAAC,CAAC,CAAC;QAClE,CAAC;QAED,IAAI,EAAE,CAAC;IACT,CAAC,CAAC;AACJ,CAAC,CAAC;AAfW,QAAA,cAAc,kBAezB;AAGK,MAAM,YAAY,GAAG,KAAK,EAC/B,GAAY,EACZ,GAAa,EACb,IAAkB,EAClB,EAAE;IACF,IAAI,CAAC;QACH,MAAM,UAAU,GAAG,GAAG,CAAC,OAAO,CAAC,aAAa,CAAC;QAC7C,MAAM,KAAK,GAAG,UAAU,IAAI,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAErD,IAAI,KAAK,EAAE,CAAC;YACV,MAAM,OAAO,GAAG,sBAAG,CAAC,MAAM,CAAC,KAAK,EAAE,eAAM,CAAC,SAAS,CAAe,CAAC;YAElE,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;gBACxC,KAAK,EAAE;oBACL,EAAE,EAAE,OAAO,CAAC,MAAM;oBAClB,QAAQ,EAAE,IAAI;iBACf;gBACD,OAAO,EAAE;oBACP,KAAK,EAAE;wBACL,OAAO,EAAE;4BACP,IAAI,EAAE;gCACJ,OAAO,EAAE;oCACP,WAAW,EAAE;wCACX,OAAO,EAAE;4CACP,UAAU,EAAE,IAAI;yCACjB;qCACF;iCACF;6BACF;yBACF;qBACF;iBACF;aACF,CAAC,CAAC;YAEH,IAAI,IAAI,EAAE,CAAC;gBACT,MAAM,WAAW,GAAG,IAAA,iCAAkB,EACpC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;oBACpB,WAAW,EAAE,EAAE,CAAC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,UAAU,CAAC;iBAC1D,CAAC,CAAC,CACJ,CAAC;gBAEF,GAAG,CAAC,IAAI,GAAG;oBACT,EAAE,EAAE,IAAI,CAAC,EAAE;oBACX,KAAK,EAAE,IAAI,CAAC,KAAK;oBACjB,SAAS,EAAE,IAAI,CAAC,SAAS;oBACzB,QAAQ,EAAE,IAAI,CAAC,QAAQ;oBACvB,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC;oBACzC,WAAW;iBACZ,CAAC;YACJ,CAAC;QACH,CAAC;QAED,IAAI,EAAE,CAAC;IACT,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QAEf,IAAI,EAAE,CAAC;IACT,CAAC;AACH,CAAC,CAAC;AAzDW,QAAA,YAAY,gBAyDvB"}