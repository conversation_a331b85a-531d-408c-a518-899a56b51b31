"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const client_1 = require("@prisma/client");
const bcryptjs_1 = __importDefault(require("bcryptjs"));
const shared_utils_1 = require("@ar-scia/shared-utils");
const prisma = new client_1.PrismaClient();
async function main() {
    console.log('🌱 Starting database seed...');
    console.log('📋 Creating permissions...');
    const permissions = [
        { name: 'customers:read', resource: 'customers', action: 'read' },
        { name: 'customers:write', resource: 'customers', action: 'write' },
        { name: 'customers:update', resource: 'customers', action: 'update' },
        { name: 'invoices:read', resource: 'invoices', action: 'read' },
        { name: 'invoices:write', resource: 'invoices', action: 'write' },
        { name: 'invoices:email', resource: 'invoices', action: 'email' },
        { name: 'payments:read', resource: 'payments', action: 'read' },
        { name: 'payments:write', resource: 'payments', action: 'write' },
        { name: 'shipments:read', resource: 'shipments', action: 'read' },
        { name: 'shipments:write', resource: 'shipments', action: 'write' },
        { name: 'documents:read', resource: 'documents', action: 'read' },
        { name: 'documents:write', resource: 'documents', action: 'write' },
        { name: 'documents:parse', resource: 'documents', action: 'parse' },
        { name: 'tracking:read', resource: 'tracking', action: 'read' },
        { name: 'tracking:write', resource: 'tracking', action: 'write' },
        { name: 'exceptions:read', resource: 'exceptions', action: 'read' },
        { name: 'exceptions:write', resource: 'exceptions', action: 'write' },
        { name: 'exceptions:update', resource: 'exceptions', action: 'update' },
        { name: 'communications:read', resource: 'communications', action: 'read' },
        { name: 'communications:write', resource: 'communications', action: 'write' },
        { name: 'analytics:ar:read', resource: 'analytics', action: 'ar:read' },
        { name: 'analytics:supply-chain:read', resource: 'analytics', action: 'supply-chain:read' },
        { name: 'reports:ar:export', resource: 'reports', action: 'ar:export' },
        { name: 'customer-360:read', resource: 'customer-360', action: 'read' },
        { name: '*', resource: '*', action: '*' },
    ];
    for (const permission of permissions) {
        await prisma.permission.upsert({
            where: { name: permission.name },
            update: {},
            create: permission,
        });
    }
    console.log('👥 Creating roles...');
    const roles = [
        {
            name: 'SUPER_ADMIN',
            description: 'Full system access',
            permissions: ['*']
        },
        {
            name: 'FINANCE_MANAGER',
            description: 'AR management and financial oversight',
            permissions: shared_utils_1.ROLE_PERMISSIONS.FINANCE_MANAGER
        },
        {
            name: 'SUPPLY_CHAIN_MANAGER',
            description: 'Supply chain operations management',
            permissions: shared_utils_1.ROLE_PERMISSIONS.SUPPLY_CHAIN_MANAGER
        },
        {
            name: 'OPERATIONS_STAFF',
            description: 'Day-to-day operations',
            permissions: shared_utils_1.ROLE_PERMISSIONS.OPERATIONS_STAFF
        },
        {
            name: 'CUSTOMER_SERVICE',
            description: 'Customer interaction and support',
            permissions: shared_utils_1.ROLE_PERMISSIONS.CUSTOMER_SERVICE
        }
    ];
    for (const roleData of roles) {
        const role = await prisma.role.upsert({
            where: { name: roleData.name },
            update: {},
            create: {
                name: roleData.name,
                description: roleData.description,
            },
        });
        for (const permissionName of roleData.permissions) {
            const permission = await prisma.permission.findUnique({
                where: { name: permissionName }
            });
            if (permission) {
                await prisma.rolePermission.upsert({
                    where: {
                        roleId_permissionId: {
                            roleId: role.id,
                            permissionId: permission.id
                        }
                    },
                    update: {},
                    create: {
                        roleId: role.id,
                        permissionId: permission.id
                    }
                });
            }
        }
    }
    console.log('👤 Creating users...');
    const hashedPassword = await bcryptjs_1.default.hash('password123', 10);
    const users = [
        {
            email: '<EMAIL>',
            firstName: 'System',
            lastName: 'Administrator',
            password: hashedPassword,
            roleName: 'SUPER_ADMIN'
        },
        {
            email: '<EMAIL>',
            firstName: 'Finance',
            lastName: 'Manager',
            password: hashedPassword,
            roleName: 'FINANCE_MANAGER'
        },
        {
            email: '<EMAIL>',
            firstName: 'Supply Chain',
            lastName: 'Manager',
            password: hashedPassword,
            roleName: 'SUPPLY_CHAIN_MANAGER'
        },
        {
            email: '<EMAIL>',
            firstName: 'Operations',
            lastName: 'Staff',
            password: hashedPassword,
            roleName: 'OPERATIONS_STAFF'
        },
        {
            email: '<EMAIL>',
            firstName: 'Customer',
            lastName: 'Service',
            password: hashedPassword,
            roleName: 'CUSTOMER_SERVICE'
        }
    ];
    for (const userData of users) {
        const user = await prisma.user.upsert({
            where: { email: userData.email },
            update: {},
            create: {
                email: userData.email,
                firstName: userData.firstName,
                lastName: userData.lastName,
                password: userData.password,
            },
        });
        const role = await prisma.role.findUnique({
            where: { name: userData.roleName }
        });
        if (role) {
            await prisma.userRole.upsert({
                where: {
                    userId_roleId: {
                        userId: user.id,
                        roleId: role.id
                    }
                },
                update: {},
                create: {
                    userId: user.id,
                    roleId: role.id
                }
            });
        }
    }
    console.log('🏢 Creating customers...');
    const customers = [
        {
            name: 'Acme Corporation',
            email: '<EMAIL>',
            phone: '******-0123',
            address: '123 Business St, New York, NY 10001',
            priority: 'HIGH'
        },
        {
            name: 'Global Industries Ltd',
            email: '<EMAIL>',
            phone: '******-0456',
            address: '456 Commerce Ave, Los Angeles, CA 90210',
            priority: 'CRITICAL'
        },
        {
            name: 'Tech Solutions Inc',
            email: '<EMAIL>',
            phone: '******-0789',
            address: '789 Innovation Dr, Austin, TX 73301',
            priority: 'NORMAL'
        },
        {
            name: 'Manufacturing Co',
            email: '<EMAIL>',
            phone: '******-0321',
            address: '321 Industrial Blvd, Detroit, MI 48201',
            priority: 'HIGH'
        },
        {
            name: 'Retail Partners LLC',
            email: '<EMAIL>',
            phone: '******-0654',
            address: '654 Market St, Chicago, IL 60601',
            priority: 'NORMAL'
        }
    ];
    const createdCustomers = [];
    for (const customerData of customers) {
        const customer = await prisma.customer.create({
            data: {
                name: customerData.name,
                email: customerData.email,
                phone: customerData.phone,
                address: customerData.address,
                priority: customerData.priority,
            },
        });
        createdCustomers.push(customer);
    }
    console.log('📄 Creating invoices...');
    const invoices = [];
    for (let i = 0; i < createdCustomers.length; i++) {
        const customer = createdCustomers[i];
        const invoiceCount = Math.floor(Math.random() * 3) + 2;
        for (let j = 0; j < invoiceCount; j++) {
            const dueDate = new Date();
            dueDate.setDate(dueDate.getDate() + (Math.random() * 60 - 30));
            const amount = Math.floor(Math.random() * 50000) + 5000;
            const isOverdue = dueDate < new Date();
            const invoice = await prisma.invoice.create({
                data: {
                    invoiceNumber: `INV-2024-${String(i * 10 + j + 1).padStart(4, '0')}`,
                    customerId: customer.id,
                    amount: amount,
                    dueDate: dueDate,
                    status: isOverdue ? 'OVERDUE' : 'PENDING',
                    xeroId: `xero_${Math.random().toString(36).substring(7)}`,
                },
            });
            invoices.push(invoice);
        }
    }
    console.log('💰 Creating payments...');
    const paidInvoices = invoices.slice(0, Math.floor(invoices.length * 0.6));
    for (const invoice of paidInvoices) {
        const paymentMethods = ['BANK_TRANSFER', 'CREDIT_CARD', 'CHECK'];
        const method = paymentMethods[Math.floor(Math.random() * paymentMethods.length)];
        await prisma.payment.create({
            data: {
                invoiceId: invoice.id,
                amount: invoice.amount,
                paymentDate: new Date(invoice.createdAt.getTime() + Math.random() * 30 * 24 * 60 * 60 * 1000),
                method: method,
                reference: `PAY-${Math.random().toString(36).substring(7).toUpperCase()}`,
            },
        });
        await prisma.invoice.update({
            where: { id: invoice.id },
            data: { status: 'PAID' },
        });
    }
    console.log('🚢 Creating shipments...');
    for (const customer of createdCustomers) {
        const shipmentCount = Math.floor(Math.random() * 4) + 1;
        for (let i = 0; i < shipmentCount; i++) {
            const origins = ['Shanghai, China', 'Hamburg, Germany', 'Los Angeles, USA', 'Singapore'];
            const destinations = ['New York, USA', 'Long Beach, USA', 'Houston, USA', 'Miami, USA'];
            const origin = origins[Math.floor(Math.random() * origins.length)];
            const destination = destinations[Math.floor(Math.random() * destinations.length)];
            const estimatedDate = new Date();
            estimatedDate.setDate(estimatedDate.getDate() + Math.random() * 30 + 5);
            const statuses = ['PENDING', 'IN_TRANSIT', 'DELIVERED', 'DELAYED'];
            const status = statuses[Math.floor(Math.random() * statuses.length)];
            const shipment = await prisma.shipment.create({
                data: {
                    shipmentNumber: `SHP-${customer.name.substring(0, 3).toUpperCase()}-${String(i + 1).padStart(3, '0')}`,
                    customerId: customer.id,
                    containerNumber: `CONT${Math.random().toString(36).substring(2, 8).toUpperCase()}`,
                    status: status,
                    origin: origin,
                    destination: destination,
                    estimatedDate: estimatedDate,
                    actualDate: status === 'DELIVERED' ? new Date() : null,
                },
            });
            const eventCount = Math.floor(Math.random() * 5) + 2;
            for (let j = 0; j < eventCount; j++) {
                const eventDate = new Date(shipment.createdAt.getTime() + j * 2 * 24 * 60 * 60 * 1000);
                await prisma.trackingEvent.create({
                    data: {
                        shipmentId: shipment.id,
                        location: j === 0 ? origin : j === eventCount - 1 ? destination : `Transit Point ${j}`,
                        status: j === 0 ? 'DEPARTED' : j === eventCount - 1 ? 'ARRIVED' : 'IN_TRANSIT',
                        description: `Container processed at ${j === 0 ? origin : j === eventCount - 1 ? destination : `Transit Point ${j}`}`,
                        timestamp: eventDate,
                    },
                });
            }
        }
    }
    console.log('✅ Database seed completed successfully!');
    console.log('📊 Created:');
    console.log(`   - ${permissions.length} permissions`);
    console.log(`   - ${roles.length} roles`);
    console.log(`   - ${users.length} users`);
    console.log(`   - ${customers.length} customers`);
    console.log(`   - ${invoices.length} invoices`);
    console.log(`   - ${paidInvoices.length} payments`);
    console.log(`   - Multiple shipments and tracking events`);
    console.log('');
    console.log('🔑 Login credentials:');
    console.log('   Email: <EMAIL>');
    console.log('   Password: password123');
}
main()
    .catch((e) => {
    console.error('❌ Seed failed:', e);
    process.exit(1);
})
    .finally(async () => {
    await prisma.$disconnect();
});
//# sourceMappingURL=seed.js.map