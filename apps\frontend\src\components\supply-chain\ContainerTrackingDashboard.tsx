import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Progress } from '@/components/ui/progress';
import { 
  Ship, Truck, Plane, MapPin, Clock, AlertTriangle, CheckCircle, 
  Package, Search, Filter, Download, Eye, MoreHorizontal,
  Navigation, Anchor, Container, Globe, Calendar, TrendingUp
} from 'lucide-react';
import { format } from 'date-fns';

interface ContainerShipment {
  id: string;
  containerNumber: string;
  orderNumber: string;
  customerId: string;
  customerName: string;
  vesselName: string;
  voyageNumber: string;
  portOfLoading: string;
  portOfDischarge: string;
  estimatedDeparture: Date;
  estimatedArrival: Date;
  actualDeparture?: Date;
  actualArrival?: Date;
  status: 'planned' | 'loading' | 'in_transit' | 'arrived' | 'discharged' | 'delivered' | 'exception';
  priority: 'low' | 'medium' | 'high' | 'critical';
  value: number;
  weight: number;
  volume: number;
  milestones: TrackingMilestone[];
  currentLocation?: {
    latitude: number;
    longitude: number;
    locationName: string;
    timestamp: Date;
  };
  exceptions: number;
  documents: {
    total: number;
    received: number;
    missing: number;
  };
}

interface TrackingMilestone {
  id: string;
  type: 'booking_confirmed' | 'cargo_loaded' | 'vessel_departed' | 'in_transit' | 'port_arrival' | 'customs_cleared' | 'cargo_discharged' | 'delivered';
  name: string;
  expectedDate: Date;
  actualDate?: Date;
  location: string;
  status: 'pending' | 'in_progress' | 'completed' | 'delayed' | 'exception';
  description?: string;
  estimatedDelay?: number;
}

interface TrackingMetrics {
  totalShipments: number;
  inTransit: number;
  onTime: number;
  delayed: number;
  exceptions: number;
  averageTransitTime: number;
  onTimePerformance: number;
}

export const ContainerTrackingDashboard: React.FC = () => {
  const [shipments, setShipments] = useState<ContainerShipment[]>([]);
  const [metrics, setMetrics] = useState<TrackingMetrics | null>(null);
  const [selectedShipment, setSelectedShipment] = useState<ContainerShipment | null>(null);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [priorityFilter, setPriorityFilter] = useState('all');

  useEffect(() => {
    loadTrackingData();
  }, []);

  const loadTrackingData = async () => {
    setLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const mockShipments: ContainerShipment[] = [
        {
          id: '1',
          containerNumber: 'MSCU1234567',
          orderNumber: 'ORD-2024-001',
          customerId: 'cust-1',
          customerName: 'Acme Corporation',
          vesselName: 'MSC OSCAR',
          voyageNumber: 'MSC001E',
          portOfLoading: 'Shanghai, China',
          portOfDischarge: 'Los Angeles, USA',
          estimatedDeparture: new Date('2024-02-15'),
          estimatedArrival: new Date('2024-03-01'),
          actualDeparture: new Date('2024-02-16'),
          status: 'in_transit',
          priority: 'high',
          value: 125000,
          weight: 18500,
          volume: 28.5,
          currentLocation: {
            latitude: 35.6762,
            longitude: 139.6503,
            locationName: 'Pacific Ocean - 500nm from Tokyo',
            timestamp: new Date()
          },
          exceptions: 1,
          documents: { total: 8, received: 6, missing: 2 },
          milestones: [
            {
              id: 'm1',
              type: 'booking_confirmed',
              name: 'Booking Confirmed',
              expectedDate: new Date('2024-02-10'),
              actualDate: new Date('2024-02-10'),
              location: 'Shanghai, China',
              status: 'completed'
            },
            {
              id: 'm2',
              type: 'cargo_loaded',
              name: 'Cargo Loaded',
              expectedDate: new Date('2024-02-14'),
              actualDate: new Date('2024-02-15'),
              location: 'Shanghai Port',
              status: 'completed'
            },
            {
              id: 'm3',
              type: 'vessel_departed',
              name: 'Vessel Departed',
              expectedDate: new Date('2024-02-15'),
              actualDate: new Date('2024-02-16'),
              location: 'Shanghai Port',
              status: 'completed',
              estimatedDelay: 1
            },
            {
              id: 'm4',
              type: 'in_transit',
              name: 'In Transit',
              expectedDate: new Date('2024-02-16'),
              actualDate: new Date('2024-02-16'),
              location: 'Pacific Ocean',
              status: 'in_progress'
            },
            {
              id: 'm5',
              type: 'port_arrival',
              name: 'Port Arrival',
              expectedDate: new Date('2024-03-01'),
              location: 'Los Angeles Port',
              status: 'pending'
            }
          ]
        },
        {
          id: '2',
          containerNumber: 'CMAU9876543',
          orderNumber: 'ORD-2024-002',
          customerId: 'cust-2',
          customerName: 'Global Industries',
          vesselName: 'CMA CGM MARCO POLO',
          voyageNumber: 'CMA002W',
          portOfLoading: 'Hamburg, Germany',
          portOfDischarge: 'New York, USA',
          estimatedDeparture: new Date('2024-02-20'),
          estimatedArrival: new Date('2024-03-05'),
          status: 'loading',
          priority: 'medium',
          value: 85000,
          weight: 15200,
          volume: 22.8,
          exceptions: 0,
          documents: { total: 6, received: 6, missing: 0 },
          milestones: [
            {
              id: 'm6',
              type: 'booking_confirmed',
              name: 'Booking Confirmed',
              expectedDate: new Date('2024-02-15'),
              actualDate: new Date('2024-02-15'),
              location: 'Hamburg, Germany',
              status: 'completed'
            },
            {
              id: 'm7',
              type: 'cargo_loaded',
              name: 'Cargo Loading',
              expectedDate: new Date('2024-02-19'),
              location: 'Hamburg Port',
              status: 'in_progress'
            }
          ]
        }
      ];

      setShipments(mockShipments);

      setMetrics({
        totalShipments: 45,
        inTransit: 28,
        onTime: 38,
        delayed: 7,
        exceptions: 12,
        averageTransitTime: 18.5,
        onTimePerformance: 84.4
      });

    } catch (error) {
      console.error('Failed to load tracking data:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusBadge = (status: ContainerShipment['status']) => {
    const variants = {
      planned: 'secondary',
      loading: 'default',
      in_transit: 'default',
      arrived: 'secondary',
      discharged: 'secondary',
      delivered: 'success',
      exception: 'destructive'
    } as const;

    const icons = {
      planned: Calendar,
      loading: Package,
      in_transit: Ship,
      arrived: Anchor,
      discharged: Container,
      delivered: CheckCircle,
      exception: AlertTriangle
    };

    const Icon = icons[status];

    return (
      <Badge variant={variants[status]}>
        <Icon className="w-3 h-3 mr-1" />
        {status.replace('_', ' ').charAt(0).toUpperCase() + status.replace('_', ' ').slice(1)}
      </Badge>
    );
  };

  const getPriorityBadge = (priority: string) => {
    const variants = {
      low: 'default',
      medium: 'secondary',
      high: 'destructive',
      critical: 'destructive'
    } as const;

    return (
      <Badge variant={variants[priority as keyof typeof variants] || 'default'}>
        {priority.charAt(0).toUpperCase() + priority.slice(1)}
      </Badge>
    );
  };

  const getMilestoneProgress = (milestones: TrackingMilestone[]) => {
    const completed = milestones.filter(m => m.status === 'completed').length;
    return (completed / milestones.length) * 100;
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  const filteredShipments = shipments.filter(shipment => {
    const matchesSearch = searchTerm === '' || 
      shipment.containerNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
      shipment.orderNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
      shipment.customerName.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = statusFilter === 'all' || shipment.status === statusFilter;
    const matchesPriority = priorityFilter === 'all' || shipment.priority === priorityFilter;
    
    return matchesSearch && matchesStatus && matchesPriority;
  });

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="mt-2 text-muted-foreground">Loading container tracking data...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Container Tracking Dashboard</h1>
          <p className="text-muted-foreground">Real-time visibility into container shipments and logistics</p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline">
            <Download className="w-4 h-4 mr-2" />
            Export
          </Button>
          <Button>
            <Search className="w-4 h-4 mr-2" />
            Track Container
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      {metrics && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Shipments</CardTitle>
              <Container className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{metrics.totalShipments}</div>
              <p className="text-xs text-muted-foreground">
                {metrics.inTransit} in transit
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">On-Time Performance</CardTitle>
              <TrendingUp className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">{metrics.onTimePerformance}%</div>
              <p className="text-xs text-muted-foreground">
                {metrics.onTime} on time, {metrics.delayed} delayed
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Avg Transit Time</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{metrics.averageTransitTime} days</div>
              <p className="text-xs text-muted-foreground">
                Target: 20 days
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active Exceptions</CardTitle>
              <AlertTriangle className="h-4 w-4 text-destructive" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-destructive">{metrics.exceptions}</div>
              <p className="text-xs text-muted-foreground">
                Require attention
              </p>
            </CardContent>
          </Card>
        </div>
      )}

      <Tabs defaultValue="overview" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="tracking">Live Tracking</TabsTrigger>
          <TabsTrigger value="timeline">Timeline View</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Container Shipments</CardTitle>
              <CardDescription>Monitor all container shipments and their current status</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex items-center gap-4 mb-4">
                <div className="flex items-center gap-2">
                  <Search className="w-4 h-4 text-muted-foreground" />
                  <Input
                    placeholder="Search containers, orders, customers..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="max-w-sm"
                  />
                </div>
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger className="w-32">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Status</SelectItem>
                    <SelectItem value="planned">Planned</SelectItem>
                    <SelectItem value="loading">Loading</SelectItem>
                    <SelectItem value="in_transit">In Transit</SelectItem>
                    <SelectItem value="arrived">Arrived</SelectItem>
                    <SelectItem value="delivered">Delivered</SelectItem>
                    <SelectItem value="exception">Exception</SelectItem>
                  </SelectContent>
                </Select>
                <Select value={priorityFilter} onValueChange={setPriorityFilter}>
                  <SelectTrigger className="w-32">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Priority</SelectItem>
                    <SelectItem value="low">Low</SelectItem>
                    <SelectItem value="medium">Medium</SelectItem>
                    <SelectItem value="high">High</SelectItem>
                    <SelectItem value="critical">Critical</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b">
                      <th className="text-left p-2">Container</th>
                      <th className="text-left p-2">Customer</th>
                      <th className="text-left p-2">Route</th>
                      <th className="text-center p-2">Progress</th>
                      <th className="text-center p-2">Status</th>
                      <th className="text-center p-2">ETA</th>
                      <th className="text-center p-2">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {filteredShipments.map((shipment) => (
                      <tr key={shipment.id} className="border-b hover:bg-muted/50">
                        <td className="p-2">
                          <div>
                            <div className="font-medium">{shipment.containerNumber}</div>
                            <div className="text-sm text-muted-foreground">{shipment.orderNumber}</div>
                          </div>
                        </td>
                        <td className="p-2">
                          <div>
                            <div className="font-medium">{shipment.customerName}</div>
                            <div className="text-sm text-muted-foreground">
                              {formatCurrency(shipment.value)}
                            </div>
                          </div>
                        </td>
                        <td className="p-2">
                          <div>
                            <div className="font-medium">{shipment.portOfLoading}</div>
                            <div className="text-sm text-muted-foreground flex items-center">
                              <Navigation className="w-3 h-3 mr-1" />
                              {shipment.portOfDischarge}
                            </div>
                          </div>
                        </td>
                        <td className="text-center p-2">
                          <div className="space-y-1">
                            <Progress value={getMilestoneProgress(shipment.milestones)} className="w-20" />
                            <div className="text-xs text-muted-foreground">
                              {Math.round(getMilestoneProgress(shipment.milestones))}%
                            </div>
                          </div>
                        </td>
                        <td className="text-center p-2">
                          <div className="space-y-1">
                            {getStatusBadge(shipment.status)}
                            {getPriorityBadge(shipment.priority)}
                          </div>
                        </td>
                        <td className="text-center p-2">
                          <div>
                            <div className="font-medium">
                              {format(shipment.estimatedArrival, 'MMM dd')}
                            </div>
                            <div className="text-sm text-muted-foreground">
                              {Math.ceil((shipment.estimatedArrival.getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24))} days
                            </div>
                          </div>
                        </td>
                        <td className="text-center p-2">
                          <div className="flex items-center justify-center gap-1">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => setSelectedShipment(shipment)}
                            >
                              <Eye className="w-4 h-4" />
                            </Button>
                            <Button variant="ghost" size="sm">
                              <MapPin className="w-4 h-4" />
                            </Button>
                            {shipment.exceptions > 0 && (
                              <Badge variant="destructive" className="text-xs">
                                {shipment.exceptions}
                              </Badge>
                            )}
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="tracking" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Live Container Locations</CardTitle>
                <CardDescription>Real-time positions of containers in transit</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-64 bg-muted rounded-lg flex items-center justify-center">
                  <div className="text-center">
                    <Globe className="w-12 h-12 mx-auto mb-2 text-muted-foreground" />
                    <p className="text-muted-foreground">Interactive map would be displayed here</p>
                    <p className="text-sm text-muted-foreground">Showing real-time container positions</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Vessel Information</CardTitle>
                <CardDescription>Current vessel details and schedules</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {shipments.filter(s => s.status === 'in_transit').map((shipment) => (
                    <div key={shipment.id} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="space-y-1">
                        <div className="font-medium">{shipment.vesselName}</div>
                        <div className="text-sm text-muted-foreground">
                          Voyage: {shipment.voyageNumber}
                        </div>
                        {shipment.currentLocation && (
                          <div className="text-sm text-muted-foreground">
                            <MapPin className="w-3 h-3 inline mr-1" />
                            {shipment.currentLocation.locationName}
                          </div>
                        )}
                      </div>
                      <div className="text-right">
                        <div className="text-sm font-medium">
                          ETA: {format(shipment.estimatedArrival, 'MMM dd, HH:mm')}
                        </div>
                        <div className="text-xs text-muted-foreground">
                          Container: {shipment.containerNumber}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="timeline" className="space-y-4">
          {selectedShipment ? (
            <Card>
              <CardHeader>
                <CardTitle>Shipment Timeline - {selectedShipment.containerNumber}</CardTitle>
                <CardDescription>
                  Detailed milestone tracking for {selectedShipment.customerName}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {selectedShipment.milestones.map((milestone, index) => (
                    <div key={milestone.id} className="flex items-start gap-4">
                      <div className="flex flex-col items-center">
                        <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                          milestone.status === 'completed' ? 'bg-green-100 text-green-600' :
                          milestone.status === 'in_progress' ? 'bg-blue-100 text-blue-600' :
                          milestone.status === 'delayed' ? 'bg-red-100 text-red-600' :
                          'bg-gray-100 text-gray-600'
                        }`}>
                          {milestone.status === 'completed' ? (
                            <CheckCircle className="w-4 h-4" />
                          ) : milestone.status === 'in_progress' ? (
                            <Clock className="w-4 h-4" />
                          ) : milestone.status === 'delayed' ? (
                            <AlertTriangle className="w-4 h-4" />
                          ) : (
                            <div className="w-2 h-2 rounded-full bg-current" />
                          )}
                        </div>
                        {index < selectedShipment.milestones.length - 1 && (
                          <div className="w-px h-8 bg-border mt-2" />
                        )}
                      </div>
                      <div className="flex-1 space-y-1">
                        <div className="flex items-center justify-between">
                          <div className="font-medium">{milestone.name}</div>
                          <div className="text-sm text-muted-foreground">
                            {milestone.actualDate ? 
                              format(milestone.actualDate, 'MMM dd, HH:mm') :
                              format(milestone.expectedDate, 'MMM dd, HH:mm')
                            }
                          </div>
                        </div>
                        <div className="text-sm text-muted-foreground">
                          <MapPin className="w-3 h-3 inline mr-1" />
                          {milestone.location}
                        </div>
                        {milestone.description && (
                          <div className="text-sm text-muted-foreground">
                            {milestone.description}
                          </div>
                        )}
                        {milestone.estimatedDelay && (
                          <div className="text-sm text-red-600">
                            Delayed by {milestone.estimatedDelay} day(s)
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          ) : (
            <Card>
              <CardContent className="text-center py-8">
                <Container className="w-12 h-12 mx-auto mb-4 text-muted-foreground" />
                <p className="text-muted-foreground">Select a shipment to view its timeline</p>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Transit Time Analysis</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between">
                    <span>Average Transit Time</span>
                    <span className="font-medium">18.5 days</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Fastest Route</span>
                    <span className="font-medium">12 days</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Slowest Route</span>
                    <span className="font-medium">28 days</span>
                  </div>
                  <div className="flex justify-between">
                    <span>On-Time Delivery</span>
                    <span className="font-medium text-green-600">84.4%</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Exception Summary</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between">
                    <span>Weather Delays</span>
                    <span className="font-medium">5</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Port Congestion</span>
                    <span className="font-medium">3</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Documentation Issues</span>
                    <span className="font-medium">2</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Customs Delays</span>
                    <span className="font-medium">2</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};
