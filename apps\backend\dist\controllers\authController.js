"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getCurrentUser = exports.refreshToken = exports.logout = exports.register = exports.login = void 0;
const authService_1 = require("@/services/authService");
const shared_utils_1 = require("@ar-scia/shared-utils");
const errorHandler_1 = require("@/middleware/errorHandler");
const authService = new authService_1.AuthService();
exports.login = (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const loginData = req.body;
    const result = await authService.login(loginData);
    res.json((0, shared_utils_1.createApiResponse)(true, result, undefined, 'Login successful'));
});
exports.register = (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const validatedData = shared_utils_1.userSchema.parse(req.body);
    const registerData = {
        email: validatedData.email,
        password: validatedData.password,
        firstName: validatedData.firstName,
        lastName: validatedData.lastName
    };
    const result = await authService.register(registerData);
    res.status(201).json((0, shared_utils_1.createApiResponse)(true, result, undefined, 'Registration successful'));
});
exports.logout = (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1];
    if (token) {
        await authService.logout(token);
    }
    res.json((0, shared_utils_1.createApiResponse)(true, undefined, undefined, 'Logout successful'));
});
exports.refreshToken = (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1];
    if (!token) {
        return res.status(401).json((0, shared_utils_1.createApiResponse)(false, undefined, 'Token required'));
    }
    const result = await authService.refreshToken(token);
    res.json((0, shared_utils_1.createApiResponse)(true, result, undefined, 'Token refreshed successfully'));
});
exports.getCurrentUser = (0, errorHandler_1.asyncHandler)(async (req, res) => {
    if (!req.user) {
        return res.status(401).json((0, shared_utils_1.createApiResponse)(false, undefined, 'Authentication required'));
    }
    const user = await authService.getCurrentUser(req.user.id);
    res.json((0, shared_utils_1.createApiResponse)(true, user, undefined, 'User retrieved successfully'));
});
//# sourceMappingURL=authController.js.map