"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.customerRoutes = void 0;
const express_1 = require("express");
const auth_1 = require("@/middleware/auth");
const customerController_1 = require("@/controllers/customerController");
const router = (0, express_1.Router)();
exports.customerRoutes = router;
router.use(auth_1.authenticateToken);
router.get('/', (0, auth_1.requirePermission)('customers:read'), customerController_1.getCustomers);
router.get('/:id', (0, auth_1.requirePermission)('customers:read'), customerController_1.getCustomerById);
router.post('/', (0, auth_1.requirePermission)('customers:write'), customerController_1.createCustomer);
router.put('/:id', (0, auth_1.requirePermission)('customers:write'), customerController_1.updateCustomer);
router.delete('/:id', (0, auth_1.requirePermission)('customers:write'), customerController_1.deleteCustomer);
router.get('/:id/360', (0, auth_1.requirePermission)('customer-360:read'), customerController_1.getCustomer360);
//# sourceMappingURL=customerRoutes.js.map