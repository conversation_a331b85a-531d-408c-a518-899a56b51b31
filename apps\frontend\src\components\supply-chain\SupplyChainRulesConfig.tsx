import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Settings, Plus, Edit, Trash2, Save, AlertTriangle, CheckCircle, 
  Clock, Users, Package, Truck, Globe, Target, Zap, Shield,
  Filter, Search, MoreHorizontal, Copy, Download, Upload
} from 'lucide-react';

interface SupplyChainRule {
  id: string;
  name: string;
  description: string;
  category: 'timing' | 'priority' | 'exception' | 'routing' | 'cost' | 'quality';
  type: 'delay' | 'document' | 'customs' | 'quality' | 'cost' | 'priority' | 'routing';
  enabled: boolean;
  priority: number;
  conditions: RuleCondition[];
  actions: RuleAction[];
  aiEnhanced: boolean;
  createdDate: Date;
  lastModified: Date;
  createdBy: string;
  appliedCount: number;
  successRate: number;
}

interface RuleCondition {
  id: string;
  field: string;
  operator: 'equals' | 'not_equals' | 'greater_than' | 'less_than' | 'contains' | 'missing' | 'expired' | 'between';
  value: any;
  logicalOperator?: 'AND' | 'OR';
  description: string;
}

interface RuleAction {
  id: string;
  type: 'create_exception' | 'send_notification' | 'escalate' | 'update_status' | 'schedule_followup' | 'assign_priority' | 'reroute' | 'hold_shipment';
  parameters: Record<string, any>;
  description: string;
}

interface RuleTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  template: Partial<SupplyChainRule>;
}

export const SupplyChainRulesConfig: React.FC = () => {
  const [rules, setRules] = useState<SupplyChainRule[]>([]);
  const [templates, setTemplates] = useState<RuleTemplate[]>([]);
  const [selectedRule, setSelectedRule] = useState<SupplyChainRule | null>(null);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [categoryFilter, setCategoryFilter] = useState('all');
  const [enabledFilter, setEnabledFilter] = useState('all');

  useEffect(() => {
    loadRulesData();
  }, []);

  const loadRulesData = async () => {
    setLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const mockRules: SupplyChainRule[] = [
        {
          id: '1',
          name: 'High Priority Customer Expedite',
          description: 'Automatically expedite shipments for high-priority customers',
          category: 'priority',
          type: 'priority',
          enabled: true,
          priority: 1,
          conditions: [
            {
              id: 'c1',
              field: 'customer.priority',
              operator: 'equals',
              value: 'high',
              description: 'Customer priority is high'
            },
            {
              id: 'c2',
              field: 'shipment.value',
              operator: 'greater_than',
              value: 50000,
              logicalOperator: 'AND',
              description: 'Shipment value > $50,000'
            }
          ],
          actions: [
            {
              id: 'a1',
              type: 'assign_priority',
              parameters: { priority: 'critical', expedite: true },
              description: 'Set priority to critical and expedite'
            },
            {
              id: 'a2',
              type: 'send_notification',
              parameters: { recipients: ['<EMAIL>'], template: 'high_priority_alert' },
              description: 'Notify logistics team'
            }
          ],
          aiEnhanced: true,
          createdDate: new Date('2024-01-15'),
          lastModified: new Date('2024-02-01'),
          createdBy: 'John Smith',
          appliedCount: 45,
          successRate: 94.2
        },
        {
          id: '2',
          name: 'Delayed Shipment Alert',
          description: 'Create exception when shipment is delayed beyond threshold',
          category: 'timing',
          type: 'delay',
          enabled: true,
          priority: 2,
          conditions: [
            {
              id: 'c3',
              field: 'shipment.estimatedArrival',
              operator: 'less_than',
              value: 'current_date',
              description: 'Estimated arrival is past due'
            },
            {
              id: 'c4',
              field: 'shipment.status',
              operator: 'not_equals',
              value: 'delivered',
              logicalOperator: 'AND',
              description: 'Shipment not yet delivered'
            }
          ],
          actions: [
            {
              id: 'a3',
              type: 'create_exception',
              parameters: { type: 'delay', severity: 'high' },
              description: 'Create delay exception'
            },
            {
              id: 'a4',
              type: 'escalate',
              parameters: { level: 'manager', department: 'logistics' },
              description: 'Escalate to logistics manager'
            }
          ],
          aiEnhanced: false,
          createdDate: new Date('2024-01-10'),
          lastModified: new Date('2024-01-25'),
          createdBy: 'Sarah Johnson',
          appliedCount: 23,
          successRate: 87.5
        },
        {
          id: '3',
          name: 'Missing Documentation Check',
          description: 'Flag shipments with missing required documents',
          category: 'exception',
          type: 'document',
          enabled: true,
          priority: 3,
          conditions: [
            {
              id: 'c5',
              field: 'documents.required',
              operator: 'missing',
              value: true,
              description: 'Required documents are missing'
            }
          ],
          actions: [
            {
              id: 'a5',
              type: 'hold_shipment',
              parameters: { reason: 'missing_documents' },
              description: 'Hold shipment until documents received'
            },
            {
              id: 'a6',
              type: 'send_notification',
              parameters: { recipients: ['<EMAIL>'], urgency: 'high' },
              description: 'Alert documentation team'
            }
          ],
          aiEnhanced: true,
          createdDate: new Date('2024-01-20'),
          lastModified: new Date('2024-02-05'),
          createdBy: 'Mike Chen',
          appliedCount: 67,
          successRate: 96.8
        }
      ];

      const mockTemplates: RuleTemplate[] = [
        {
          id: 't1',
          name: 'Weather Delay Response',
          description: 'Automatically handle weather-related delays',
          category: 'timing',
          template: {
            category: 'timing',
            type: 'delay',
            conditions: [
              {
                id: 'tc1',
                field: 'weather.severity',
                operator: 'greater_than',
                value: 'moderate',
                description: 'Weather severity above moderate'
              }
            ],
            actions: [
              {
                id: 'ta1',
                type: 'update_status',
                parameters: { status: 'weather_delay' },
                description: 'Update status to weather delay'
              }
            ]
          }
        },
        {
          id: 't2',
          name: 'Customs Hold Protocol',
          description: 'Standard response for customs holds',
          category: 'exception',
          template: {
            category: 'exception',
            type: 'customs',
            conditions: [
              {
                id: 'tc2',
                field: 'customs.status',
                operator: 'equals',
                value: 'hold',
                description: 'Customs status is hold'
              }
            ],
            actions: [
              {
                id: 'ta2',
                type: 'create_exception',
                parameters: { type: 'customs_hold', severity: 'high' },
                description: 'Create customs hold exception'
              }
            ]
          }
        }
      ];

      setRules(mockRules);
      setTemplates(mockTemplates);
    } catch (error) {
      console.error('Failed to load rules data:', error);
    } finally {
      setLoading(false);
    }
  };

  const getCategoryBadge = (category: string) => {
    const variants = {
      timing: 'default',
      priority: 'secondary',
      exception: 'destructive',
      routing: 'default',
      cost: 'secondary',
      quality: 'default'
    } as const;

    const icons = {
      timing: Clock,
      priority: Target,
      exception: AlertTriangle,
      routing: Truck,
      cost: Package,
      quality: Shield
    };

    const Icon = icons[category as keyof typeof icons] || Settings;

    return (
      <Badge variant={variants[category as keyof typeof variants] || 'default'}>
        <Icon className="w-3 h-3 mr-1" />
        {category.charAt(0).toUpperCase() + category.slice(1)}
      </Badge>
    );
  };

  const getSuccessRateColor = (rate: number) => {
    if (rate >= 90) return 'text-green-600';
    if (rate >= 75) return 'text-yellow-600';
    return 'text-red-600';
  };

  const filteredRules = rules.filter(rule => {
    const matchesSearch = searchTerm === '' || 
      rule.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      rule.description.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesCategory = categoryFilter === 'all' || rule.category === categoryFilter;
    const matchesEnabled = enabledFilter === 'all' || 
      (enabledFilter === 'enabled' && rule.enabled) ||
      (enabledFilter === 'disabled' && !rule.enabled);
    
    return matchesSearch && matchesCategory && matchesEnabled;
  });

  const handleCreateRule = () => {
    setSelectedRule(null);
    setIsCreateDialogOpen(true);
  };

  const handleEditRule = (rule: SupplyChainRule) => {
    setSelectedRule(rule);
    setIsEditDialogOpen(true);
  };

  const handleToggleRule = async (ruleId: string, enabled: boolean) => {
    try {
      // In a real implementation, call API to update rule
      setRules(prev => prev.map(rule => 
        rule.id === ruleId ? { ...rule, enabled } : rule
      ));
    } catch (error) {
      console.error('Failed to toggle rule:', error);
    }
  };

  const handleDeleteRule = async (ruleId: string) => {
    try {
      // In a real implementation, call API to delete rule
      setRules(prev => prev.filter(rule => rule.id !== ruleId));
    } catch (error) {
      console.error('Failed to delete rule:', error);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="mt-2 text-muted-foreground">Loading supply chain rules...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Supply Chain Rules Configuration</h1>
          <p className="text-muted-foreground">Configure business rules for automated supply chain management</p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline">
            <Download className="w-4 h-4 mr-2" />
            Export Rules
          </Button>
          <Button variant="outline">
            <Upload className="w-4 h-4 mr-2" />
            Import Rules
          </Button>
          <Button onClick={handleCreateRule}>
            <Plus className="w-4 h-4 mr-2" />
            Create Rule
          </Button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Rules</CardTitle>
            <Settings className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{rules.length}</div>
            <p className="text-xs text-muted-foreground">
              {rules.filter(r => r.enabled).length} active
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">AI Enhanced</CardTitle>
            <Zap className="h-4 w-4 text-yellow-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">
              {rules.filter(r => r.aiEnhanced).length}
            </div>
            <p className="text-xs text-muted-foreground">
              {Math.round((rules.filter(r => r.aiEnhanced).length / rules.length) * 100)}% of rules
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Success Rate</CardTitle>
            <Target className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {Math.round(rules.reduce((sum, r) => sum + r.successRate, 0) / rules.length)}%
            </div>
            <p className="text-xs text-muted-foreground">
              Across all rules
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Applications</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {rules.reduce((sum, r) => sum + r.appliedCount, 0)}
            </div>
            <p className="text-xs text-muted-foreground">
              This month
            </p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="rules" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="rules">Active Rules</TabsTrigger>
          <TabsTrigger value="templates">Rule Templates</TabsTrigger>
          <TabsTrigger value="analytics">Rule Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="rules" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Supply Chain Rules</CardTitle>
              <CardDescription>Manage automated business rules for supply chain operations</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex items-center gap-4 mb-4">
                <div className="flex items-center gap-2">
                  <Search className="w-4 h-4 text-muted-foreground" />
                  <Input
                    placeholder="Search rules..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="max-w-sm"
                  />
                </div>
                <Select value={categoryFilter} onValueChange={setCategoryFilter}>
                  <SelectTrigger className="w-40">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Categories</SelectItem>
                    <SelectItem value="timing">Timing</SelectItem>
                    <SelectItem value="priority">Priority</SelectItem>
                    <SelectItem value="exception">Exception</SelectItem>
                    <SelectItem value="routing">Routing</SelectItem>
                    <SelectItem value="cost">Cost</SelectItem>
                    <SelectItem value="quality">Quality</SelectItem>
                  </SelectContent>
                </Select>
                <Select value={enabledFilter} onValueChange={setEnabledFilter}>
                  <SelectTrigger className="w-32">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Status</SelectItem>
                    <SelectItem value="enabled">Enabled</SelectItem>
                    <SelectItem value="disabled">Disabled</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-4">
                {filteredRules.map((rule) => (
                  <div key={rule.id} className="border rounded-lg p-4">
                    <div className="flex items-start justify-between">
                      <div className="space-y-2 flex-1">
                        <div className="flex items-center gap-2">
                          <h3 className="font-semibold">{rule.name}</h3>
                          {getCategoryBadge(rule.category)}
                          {rule.aiEnhanced && (
                            <Badge variant="outline" className="text-yellow-600 border-yellow-600">
                              <Zap className="w-3 h-3 mr-1" />
                              AI Enhanced
                            </Badge>
                          )}
                        </div>
                        <p className="text-sm text-muted-foreground">{rule.description}</p>
                        <div className="flex items-center gap-4 text-sm text-muted-foreground">
                          <span>Priority: {rule.priority}</span>
                          <span>Applied: {rule.appliedCount} times</span>
                          <span className={getSuccessRateColor(rule.successRate)}>
                            Success: {rule.successRate}%
                          </span>
                          <span>Created by: {rule.createdBy}</span>
                        </div>
                        <div className="text-xs text-muted-foreground">
                          {rule.conditions.length} condition(s), {rule.actions.length} action(s)
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Switch
                          checked={rule.enabled}
                          onCheckedChange={(enabled) => handleToggleRule(rule.id, enabled)}
                        />
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleEditRule(rule)}
                        >
                          <Edit className="w-4 h-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDeleteRule(rule.id)}
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="templates" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Rule Templates</CardTitle>
              <CardDescription>Pre-configured rule templates for common scenarios</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {templates.map((template) => (
                  <div key={template.id} className="border rounded-lg p-4">
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <h3 className="font-semibold">{template.name}</h3>
                        {getCategoryBadge(template.category)}
                      </div>
                      <p className="text-sm text-muted-foreground">{template.description}</p>
                      <div className="flex items-center gap-2">
                        <Button size="sm" variant="outline">
                          <Copy className="w-4 h-4 mr-2" />
                          Use Template
                        </Button>
                        <Button size="sm" variant="ghost">
                          <Edit className="w-4 h-4 mr-2" />
                          Customize
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Rule Performance</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {rules.slice(0, 5).map((rule) => (
                    <div key={rule.id} className="flex items-center justify-between">
                      <div>
                        <div className="font-medium">{rule.name}</div>
                        <div className="text-sm text-muted-foreground">
                          {rule.appliedCount} applications
                        </div>
                      </div>
                      <div className={`font-medium ${getSuccessRateColor(rule.successRate)}`}>
                        {rule.successRate}%
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Category Distribution</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {['timing', 'priority', 'exception', 'routing', 'cost', 'quality'].map((category) => {
                    const count = rules.filter(r => r.category === category).length;
                    const percentage = (count / rules.length) * 100;
                    return (
                      <div key={category} className="space-y-2">
                        <div className="flex justify-between">
                          <span className="capitalize">{category}</span>
                          <span>{count} rules</span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div 
                            className="bg-primary h-2 rounded-full" 
                            style={{ width: `${percentage}%` }}
                          />
                        </div>
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};
