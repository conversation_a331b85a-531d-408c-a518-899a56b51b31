{"version": 3, "file": "requestLogger.js", "sourceRoot": "", "sources": ["../../src/middleware/requestLogger.ts"], "names": [], "mappings": ";;;AACA,4CAAyC;AAElC,MAAM,aAAa,GAAG,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;IAC/E,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;IAGzB,IAAI,eAAM,CAAC,OAAO,KAAK,YAAY,IAAI,GAAG,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;QAC9D,OAAO,IAAI,EAAE,CAAC;IAChB,CAAC;IAGD,OAAO,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,MAAM,IAAI,GAAG,CAAC,IAAI,EAAE,EAAE;QAC1C,EAAE,EAAE,GAAG,CAAC,EAAE;QACV,SAAS,EAAE,GAAG,CAAC,GAAG,CAAC,YAAY,CAAC;QAChC,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;KACpC,CAAC,CAAC;IAGH,MAAM,WAAW,GAAG,GAAG,CAAC,GAAG,CAAC;IAC5B,GAAG,CAAC,GAAG,GAAG,UAAS,KAAW,EAAE,QAAc;QAC5C,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,KAAK,CAAC;QACpC,MAAM,WAAW,GAAG,GAAG,CAAC,UAAU,IAAI,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,UAAU,IAAI,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC;QAEvF,OAAO,CAAC,GAAG,CAAC,MAAM,WAAW,IAAI,GAAG,CAAC,UAAU,IAAI,GAAG,CAAC,MAAM,IAAI,GAAG,CAAC,IAAI,MAAM,QAAQ,IAAI,CAAC,CAAC;QAE7F,WAAW,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;IAC1C,CAAC,CAAC;IAEF,IAAI,EAAE,CAAC;AACT,CAAC,CAAC;AA3BW,QAAA,aAAa,iBA2BxB"}